2025-07-17 18:30:18 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - ط¨ط¯ط، طھط´ط؛ظٹظ„ ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط´ط­ظ†ط§طھ...
2025-07-17 18:30:18 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط¹ظٹظٹظ† طھط±ظ…ظٹط² AR8MSWIN1256
2025-07-17 18:30:18 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط­ظ…ظٹظ„ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„طھط·ط¨ظٹظ‚ ط¨ظ†ط¬ط§ط­
2025-07-17 18:30:18 [AWT-EventQueue-0] INFO  c.shipment.erp.util.MessageManager - طھظ… طھط­ظ…ظٹظ„ ط±ط³ط§ط¦ظ„ ط§ظ„ظ„ط؛ط©: messages_ar.properties
2025-07-17 18:30:18 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ظ„ط؛ط© ط§ظ„ط¹ط±ط¨ظٹط©
2025-07-17 18:30:19 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ط®ط· ط§ظ„ط¹ط±ط¨ظٹ: Tahoma ط¨ط­ط¬ظ… 14
2025-07-17 18:30:19 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھظپط¹ظٹظ„ ط¯ط¹ظ… RTL
2025-07-17 18:30:19 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ظ…ط¸ظ‡ط± ط§ظ„طھط·ط¨ظٹظ‚: FlatLaf Dark
2025-07-17 18:30:20 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - ط¨ط¯ط، طھط´ط؛ظٹظ„ ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط´ط­ظ†ط§طھ...
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط¹ظٹظٹظ† طھط±ظ…ظٹط² AR8MSWIN1256
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط­ظ…ظٹظ„ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„طھط·ط¨ظٹظ‚ ط¨ظ†ط¬ط§ط­
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  c.shipment.erp.util.MessageManager - طھظ… طھط­ظ…ظٹظ„ ط±ط³ط§ط¦ظ„ ط§ظ„ظ„ط؛ط©: messages_ar.properties
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ظ„ط؛ط© ط§ظ„ط¹ط±ط¨ظٹط©
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ط®ط· ط§ظ„ط¹ط±ط¨ظٹ: Tahoma ط¨ط­ط¬ظ… 14
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھظپط¹ظٹظ„ ط¯ط¹ظ… RTL
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ظ…ط¸ظ‡ط± ط§ظ„طھط·ط¨ظٹظ‚: FlatLaf Dark
2025-07-17 18:34:31 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 18:35:19 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - ط¨ط¯ط، طھط´ط؛ظٹظ„ ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط´ط­ظ†ط§طھ...
2025-07-17 18:35:19 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط¹ظٹظٹظ† طھط±ظ…ظٹط² AR8MSWIN1256
2025-07-17 18:35:19 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط­ظ…ظٹظ„ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„طھط·ط¨ظٹظ‚ ط¨ظ†ط¬ط§ط­
2025-07-17 18:35:19 [AWT-EventQueue-0] INFO  c.shipment.erp.util.MessageManager - طھظ… طھط­ظ…ظٹظ„ ط±ط³ط§ط¦ظ„ ط§ظ„ظ„ط؛ط©: messages_ar.properties
2025-07-17 18:35:19 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ظ„ط؛ط© ط§ظ„ط¹ط±ط¨ظٹط©
2025-07-17 18:35:20 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ط®ط· ط§ظ„ط¹ط±ط¨ظٹ: Tahoma ط¨ط­ط¬ظ… 14
2025-07-17 18:35:20 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھظپط¹ظٹظ„ ط¯ط¹ظ… RTL
2025-07-17 18:35:20 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ظ…ط¸ظ‡ط± ط§ظ„طھط·ط¨ظٹظ‚: FlatLaf Dark
2025-07-17 18:35:20 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - ط¨ط¯ط، طھط´ط؛ظٹظ„ ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط´ط­ظ†ط§طھ...
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط¹ظٹظٹظ† طھط±ظ…ظٹط² AR8MSWIN1256
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط­ظ…ظٹظ„ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„طھط·ط¨ظٹظ‚ ط¨ظ†ط¬ط§ط­
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  c.shipment.erp.util.MessageManager - طھظ… طھط­ظ…ظٹظ„ ط±ط³ط§ط¦ظ„ ط§ظ„ظ„ط؛ط©: messages_ar.properties
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ظ„ط؛ط© ط§ظ„ط¹ط±ط¨ظٹط©
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ط®ط· ط§ظ„ط¹ط±ط¨ظٹ: Tahoma ط¨ط­ط¬ظ… 14
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھظپط¹ظٹظ„ ط¯ط¹ظ… RTL
2025-07-17 18:36:45 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ظ…ط¸ظ‡ط± ط§ظ„طھط·ط¨ظٹظ‚: FlatLaf Dark
2025-07-17 18:36:46 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 18:36:47 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@48419f5f
2025-07-17 18:36:47 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 18:36:47 [AWT-EventQueue-0] INFO  c.s.erp.database.DatabaseManager - طھظ… طھظ‡ظٹط¦ط© ظ…طµط¯ط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ط¨ظ†ط¬ط§ط­ ظ…ط¹ ط¯ط¹ظ… ط§ظ„طھط±ظ…ظٹط² ط§ظ„ط¹ط±ط¨ظٹ
2025-07-17 18:36:47 [AWT-EventQueue-0] DEBUG c.s.erp.database.DatabaseManager - طھظ… طھط¹ظٹظٹظ† ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط¬ظ„ط³ط© ظ„ظ„ط¯ط¹ظ… ط§ظ„ط¹ط±ط¨ظٹ
2025-07-17 18:36:47 [AWT-EventQueue-0] INFO  c.s.erp.database.DatabaseManager - ط§ط®طھط¨ط§ط± ط§ظ„ط§طھطµط§ظ„ ط¨ظ‚ط§ط¹ط¯ط© ط§ظ„ط¨ظٹط§ظ†ط§طھ: ظ†ط¬ط­
2025-07-17 18:36:47 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… ط§ظ„ط§طھطµط§ظ„ ط¨ظ‚ط§ط¹ط¯ط© ط§ظ„ط¨ظٹط§ظ†ط§طھ ط¨ظ†ط¬ط§ط­
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - ط¨ط¯ط، طھط´ط؛ظٹظ„ ظ†ط¸ط§ظ… ط¥ط¯ط§ط±ط© ط§ظ„ط´ط­ظ†ط§طھ...
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط¹ظٹظٹظ† طھط±ظ…ظٹط² AR8MSWIN1256
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  com.shipment.erp.config.AppConfig - طھظ… طھط­ظ…ظٹظ„ ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„طھط·ط¨ظٹظ‚ ط¨ظ†ط¬ط§ط­
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  c.shipment.erp.util.MessageManager - طھظ… طھط­ظ…ظٹظ„ ط±ط³ط§ط¦ظ„ ط§ظ„ظ„ط؛ط©: messages_ar.properties
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ظ„ط؛ط© ط§ظ„ط¹ط±ط¨ظٹط©
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ط§ظ„ط®ط· ط§ظ„ط¹ط±ط¨ظٹ: Tahoma ط¨ط­ط¬ظ… 14
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھظپط¹ظٹظ„ ط¯ط¹ظ… RTL
2025-07-17 18:40:12 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… طھط¹ظٹظٹظ† ظ…ط¸ظ‡ط± ط§ظ„طھط·ط¨ظٹظ‚: FlatLaf Dark
2025-07-17 18:40:13 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 18:40:14 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@69b25ac9
2025-07-17 18:40:14 [AWT-EventQueue-0] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 18:40:14 [AWT-EventQueue-0] INFO  c.s.erp.database.DatabaseManager - طھظ… طھظ‡ظٹط¦ط© ظ…طµط¯ط± ط§ظ„ط¨ظٹط§ظ†ط§طھ ط¨ظ†ط¬ط§ط­ ظ…ط¹ ط¯ط¹ظ… ط§ظ„طھط±ظ…ظٹط² ط§ظ„ط¹ط±ط¨ظٹ
2025-07-17 18:40:14 [AWT-EventQueue-0] DEBUG c.s.erp.database.DatabaseManager - طھظ… طھط¹ظٹظٹظ† ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط¬ظ„ط³ط© ظ„ظ„ط¯ط¹ظ… ط§ظ„ط¹ط±ط¨ظٹ
2025-07-17 18:40:14 [AWT-EventQueue-0] INFO  c.s.erp.database.DatabaseManager - ط§ط®طھط¨ط§ط± ط§ظ„ط§طھطµط§ظ„ ط¨ظ‚ط§ط¹ط¯ط© ط§ظ„ط¨ظٹط§ظ†ط§طھ: ظ†ط¬ط­
2025-07-17 18:40:15 [AWT-EventQueue-0] INFO  c.shipment.erp.ShipmentManagementApp - طھظ… ط§ظ„ط§طھطµط§ظ„ ط¨ظ‚ط§ط¹ط¯ط© ط§ظ„ط¨ظٹط§ظ†ط§طھ ط¨ظ†ط¬ط§ط­
2025-07-17 18:40:34 [SwingWorker-pool-1-thread-1] DEBUG c.s.erp.database.DatabaseManager - طھظ… طھط¹ظٹظٹظ† ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط¬ظ„ط³ط© ظ„ظ„ط¯ط¹ظ… ط§ظ„ط¹ط±ط¨ظٹ
2025-07-17 18:40:34 [SwingWorker-pool-1-thread-1] ERROR c.shipment.erp.ui.login.LoginDialog - ط®ط·ط£ ظپظٹ ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط¨ظٹط§ظ†ط§طھ ط§ظ„ظ…ط³طھط®ط¯ظ…
java.sql.SQLException: مجموعة أحرف غير مدعومة (أضف orai18n.jar في مسار الطبقة): AR8MSWIN1256
	at oracle.sql.CharacterSetUnknown.failCharsetUnknown(CharacterSetFactoryThin.java:239)
	at oracle.sql.CharacterSetUnknown.convert(CharacterSetFactoryThin.java:200)
	at oracle.jdbc.driver.PhysicalConnection.throughDbCharset(PhysicalConnection.java:11051)
	at oracle.jdbc.driver.PhysicalConnection.enquoteIdentifier(PhysicalConnection.java:11131)
	at oracle.jdbc.driver.OracleStatement.enquoteIdentifier(OracleStatement.java:6787)
	at oracle.jdbc.driver.OracleStatement.getColumnIndex(OracleStatement.java:4210)
	at oracle.jdbc.driver.InsensitiveScrollableResultSet.findColumn(InsensitiveScrollableResultSet.java:298)
	at oracle.jdbc.driver.GeneratedResultSet.getInt(GeneratedResultSet.java:621)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.getInt(HikariProxyResultSet.java)
	at com.shipment.erp.ui.login.LoginDialog.authenticateUser(LoginDialog.java:275)
	at com.shipment.erp.ui.login.LoginDialog$5.doInBackground(LoginDialog.java:227)
	at com.shipment.erp.ui.login.LoginDialog$5.doInBackground(LoginDialog.java:224)
	at java.desktop/javax.swing.SwingWorker$1.call(SwingWorker.java:304)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.desktop/javax.swing.SwingWorker.run(SwingWorker.java:343)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-17 18:40:35 [SwingWorker-pool-1-thread-2] DEBUG c.s.erp.database.DatabaseManager - طھظ… طھط¹ظٹظٹظ† ط¥ط¹ط¯ط§ط¯ط§طھ ط§ظ„ط¬ظ„ط³ط© ظ„ظ„ط¯ط¹ظ… ط§ظ„ط¹ط±ط¨ظٹ
2025-07-17 18:40:35 [SwingWorker-pool-1-thread-2] ERROR c.shipment.erp.ui.login.LoginDialog - ط®ط·ط£ ظپظٹ ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط¨ظٹط§ظ†ط§طھ ط§ظ„ظ…ط³طھط®ط¯ظ…
java.sql.SQLException: مجموعة أحرف غير مدعومة (أضف orai18n.jar في مسار الطبقة): AR8MSWIN1256
	at oracle.sql.CharacterSetUnknown.failCharsetUnknown(CharacterSetFactoryThin.java:239)
	at oracle.sql.CharacterSetUnknown.convert(CharacterSetFactoryThin.java:200)
	at oracle.jdbc.driver.PhysicalConnection.throughDbCharset(PhysicalConnection.java:11051)
	at oracle.jdbc.driver.PhysicalConnection.enquoteIdentifier(PhysicalConnection.java:11131)
	at oracle.jdbc.driver.OracleStatement.enquoteIdentifier(OracleStatement.java:6787)
	at oracle.jdbc.driver.OracleStatement.getColumnIndex(OracleStatement.java:4210)
	at oracle.jdbc.driver.InsensitiveScrollableResultSet.findColumn(InsensitiveScrollableResultSet.java:298)
	at oracle.jdbc.driver.GeneratedResultSet.getInt(GeneratedResultSet.java:621)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.getInt(HikariProxyResultSet.java)
	at com.shipment.erp.ui.login.LoginDialog.authenticateUser(LoginDialog.java:275)
	at com.shipment.erp.ui.login.LoginDialog$5.doInBackground(LoginDialog.java:227)
	at com.shipment.erp.ui.login.LoginDialog$5.doInBackground(LoginDialog.java:224)
	at java.desktop/javax.swing.SwingWorker$1.call(SwingWorker.java:304)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.desktop/javax.swing.SwingWorker.run(SwingWorker.java:343)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
