@echo off
chcp 65001 > nul
title نظام إدارة الشحنات - تشغيل التطبيق

echo ========================================
echo نظام إدارة الشحنات المتكامل
echo Comprehensive Shipment Management System
echo ========================================
echo الإصدار: 1.0.0
echo Version: 1.0.0
echo ========================================
echo.

echo جاري تشغيل التطبيق...
echo Starting application...
echo.

echo تحقق من متطلبات التشغيل:
echo Checking requirements:
echo.

echo 1. فحص Java...
echo    Checking Java...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo ✗ Java غير مثبت أو غير متاح في PATH
    echo ✗ Java is not installed or not in PATH
    pause
    exit /b 1
)
echo ✓ Java متاح
echo ✓ Java available
echo.

echo 2. فحص Maven...
echo    Checking Maven...
mvn -version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ! Maven غير متاح، سيتم استخدام Java مباشرة
    echo ! Maven not available, will use Java directly
    goto :run_with_java
) else (
    echo ✓ Maven متاح
    echo ✓ Maven available
    goto :run_with_maven
)

:run_with_maven
echo.
echo تشغيل التطبيق باستخدام Maven...
echo Running application with Maven...
echo.

mvn clean compile exec:java -Dexec.mainClass="com.shipment.erp.ShipmentManagementApp"
goto :end

:run_with_java
echo.
echo تجميع وتشغيل التطبيق باستخدام Java...
echo Compiling and running application with Java...
echo.

echo تجميع الملفات...
echo Compiling files...

if not exist "target\classes" mkdir target\classes

javac -cp "src\main\java;lib\*" -d target\classes src\main\java\com\shipment\erp\*.java src\main\java\com\shipment\erp\config\*.java src\main\java\com\shipment\erp\database\*.java src\main\java\com\shipment\erp\util\*.java src\main\java\com\shipment\erp\ui\*.java src\main\java\com\shipment\erp\ui\login\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل في التجميع
    echo ✗ Compilation failed
    pause
    exit /b 1
)

echo ✓ تم التجميع بنجاح
echo ✓ Compilation successful
echo.

echo نسخ ملفات الموارد...
echo Copying resource files...
xcopy /Y /E src\main\resources\* target\classes\ > nul

echo تشغيل التطبيق...
echo Running application...
java -cp "target\classes;lib\*" -Dfile.encoding=Cp1256 -Duser.language=ar -Duser.country=SA com.shipment.erp.ShipmentManagementApp

:end
echo.
if %ERRORLEVEL% EQU 0 (
    echo ✓ تم إنهاء التطبيق بنجاح
    echo ✓ Application ended successfully
) else (
    echo ✗ حدث خطأ أثناء تشغيل التطبيق
    echo ✗ An error occurred while running the application
)

echo.
pause
