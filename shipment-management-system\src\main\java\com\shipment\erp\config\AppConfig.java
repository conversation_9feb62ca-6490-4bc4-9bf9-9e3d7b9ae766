package com.shipment.erp.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Properties;

/**
 * فئة إدارة إعدادات التطبيق مع دعم ترميز AR8MSWIN1256
 * Application Configuration Manager with AR8MSWIN1256 encoding support
 * 
 * <AUTHOR> ERP Team
 * @version 1.0.0
 */
public class AppConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(AppConfig.class);
    private static AppConfig instance;
    private Properties properties;
    
    // إعدادات ترميز AR8MSWIN1256
    public static final String ARABIC_CHARSET = "Cp1256";
    public static final String ORACLE_CHARSET = "AR8MSWIN1256";
    
    private AppConfig() {
        properties = new Properties();
    }
    
    /**
     * الحصول على مثيل وحيد من فئة الإعدادات
     */
    public static synchronized AppConfig getInstance() {
        if (instance == null) {
            instance = new AppConfig();
        }
        return instance;
    }
    
    /**
     * تحميل إعدادات التطبيق
     */
    public void loadConfiguration() {
        try {
            // تحميل الإعدادات الافتراضية
            loadPropertiesFile("/application.properties");
            
            // تعيين إعدادات ترميز AR8MSWIN1256
            setupArabicEncoding();
            
            logger.info("تم تحميل إعدادات التطبيق بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل إعدادات التطبيق", e);
            // تحميل الإعدادات الافتراضية
            loadDefaultProperties();
        }
    }
    
    /**
     * تحميل ملف الخصائص
     */
    private void loadPropertiesFile(String fileName) throws IOException {
        try (InputStream inputStream = getClass().getResourceAsStream(fileName)) {
            if (inputStream != null) {
                // قراءة الملف بترميز AR8MSWIN1256
                properties.load(inputStream);
                logger.debug("تم تحميل ملف الإعدادات: {}", fileName);
            } else {
                logger.warn("لم يتم العثور على ملف الإعدادات: {}", fileName);
            }
        }
    }
    
    /**
     * تعيين إعدادات ترميز AR8MSWIN1256
     */
    private void setupArabicEncoding() {
        // تعيين الترميز الافتراضي
        System.setProperty("file.encoding", ARABIC_CHARSET);
        
        // إعدادات Oracle للترميز العربي
        System.setProperty("oracle.jdbc.defaultNChar", "true");
        System.setProperty("oracle.jdbc.convertNcharLiterals", "true");
        
        // إعدادات NLS لـ Oracle
        properties.setProperty("oracle.nls.language", "ARABIC");
        properties.setProperty("oracle.nls.territory", "SAUDI ARABIA");
        properties.setProperty("oracle.nls.characterset", ORACLE_CHARSET);
        
        logger.info("تم تعيين ترميز AR8MSWIN1256");
    }
    
    /**
     * تحميل الإعدادات الافتراضية
     */
    private void loadDefaultProperties() {
        properties.setProperty("app.name", "نظام إدارة الشحنات");
        properties.setProperty("app.version", "1.0.0");
        properties.setProperty("app.locale", "ar_SA");
        properties.setProperty("app.rtl", "true");
        
        properties.setProperty("db.url", "*************************************");
        properties.setProperty("db.username", "ship_erp");
        properties.setProperty("db.password", "ys123");
        properties.setProperty("db.driver", "oracle.jdbc.OracleDriver");
        properties.setProperty("db.charset", ORACLE_CHARSET);
        
        properties.setProperty("ui.theme", "FlatLaf Light");
        properties.setProperty("ui.font.arabic", "Tahoma");
        properties.setProperty("ui.font.size", "14");
        properties.setProperty("ui.window.maximized", "true");
        
        logger.info("تم تحميل الإعدادات الافتراضية");
    }
    
    /**
     * الحصول على قيمة خاصية نصية
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * الحصول على قيمة خاصية نصية مع قيمة افتراضية
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * الحصول على قيمة خاصية رقمية
     */
    public int getIntProperty(String key, int defaultValue) {
        try {
            String value = properties.getProperty(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            logger.warn("قيمة غير صحيحة للخاصية {}: {}", key, properties.getProperty(key));
            return defaultValue;
        }
    }
    
    /**
     * الحصول على قيمة خاصية منطقية
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        try {
            String value = properties.getProperty(key);
            return value != null ? Boolean.parseBoolean(value) : defaultValue;
        } catch (Exception e) {
            logger.warn("قيمة غير صحيحة للخاصية {}: {}", key, properties.getProperty(key));
            return defaultValue;
        }
    }
    
    /**
     * تعيين قيمة خاصية
     */
    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
    }
    
    /**
     * الحصول على سلسلة اتصال قاعدة البيانات مع إعدادات الترميز
     */
    public String getDatabaseUrl() {
        String baseUrl = getProperty("db.url");
        
        // إضافة معاملات الترميز العربي
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        
        if (!baseUrl.contains("?")) {
            urlBuilder.append("?");
        } else {
            urlBuilder.append("&");
        }
        
        // إضافة معاملات NLS للدعم العربي
        urlBuilder.append("oracle.jdbc.defaultNChar=true");
        urlBuilder.append("&oracle.jdbc.convertNcharLiterals=true");
        
        return urlBuilder.toString();
    }
    
    /**
     * الحصول على خصائص قاعدة البيانات
     */
    public Properties getDatabaseProperties() {
        Properties dbProps = new Properties();
        dbProps.setProperty("user", getProperty("db.username"));
        dbProps.setProperty("password", getProperty("db.password"));
        
        // إعدادات الترميز العربي
        dbProps.setProperty("oracle.jdbc.defaultNChar", "true");
        dbProps.setProperty("oracle.jdbc.convertNcharLiterals", "true");
        
        // إعدادات NLS
        dbProps.setProperty("oracle.sessionTimeZone", "Asia/Riyadh");
        
        return dbProps;
    }
    
    /**
     * الحصول على الترميز العربي
     */
    public Charset getArabicCharset() {
        return Charset.forName(ARABIC_CHARSET);
    }
    
    /**
     * التحقق من دعم RTL
     */
    public boolean isRTLEnabled() {
        return getBooleanProperty("app.rtl", true);
    }
    
    /**
     * الحصول على جميع الخصائص
     */
    public Properties getAllProperties() {
        return new Properties(properties);
    }
}
