package com.shipment.erp.ui.login;

import com.shipment.erp.config.AppConfig;
import com.shipment.erp.database.DatabaseManager;
import com.shipment.erp.util.MessageManager;
// import net.miglayout.swing.MigLayout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * نافذة تسجيل الدخول مع دعم RTL والترميز العربي
 * Login Dialog with RTL support and Arabic encoding
 * 
 * <AUTHOR> ERP Team
 * @version 1.0.0
 */
public class LoginDialog extends JDialog {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginDialog.class);
    
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JButton loginButton;
    private JButton cancelButton;
    private JLabel statusLabel;
    private boolean loginSuccessful = false;
    
    private MessageManager messageManager;
    private DatabaseManager databaseManager;
    
    public LoginDialog() {
        messageManager = MessageManager.getInstance();
        databaseManager = DatabaseManager.getInstance();
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupDialog();
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // تعيين اتجاه RTL للمكونات
        ComponentOrientation rtl = ComponentOrientation.RIGHT_TO_LEFT;
        
        // حقول الإدخال
        usernameField = new JTextField(20);
        usernameField.setComponentOrientation(rtl);
        usernameField.setFont(getArabicFont());
        
        passwordField = new JPasswordField(20);
        passwordField.setComponentOrientation(rtl);
        passwordField.setFont(getArabicFont());
        
        // الأزرار
        loginButton = new JButton("تسجيل الدخول");
        loginButton.setComponentOrientation(rtl);
        loginButton.setFont(getArabicFont());
        loginButton.setPreferredSize(new Dimension(120, 35));
        
        cancelButton = new JButton("إلغاء");
        cancelButton.setComponentOrientation(rtl);
        cancelButton.setFont(getArabicFont());
        cancelButton.setPreferredSize(new Dimension(120, 35));
        
        // تسمية الحالة
        statusLabel = new JLabel(" ");
        statusLabel.setComponentOrientation(rtl);
        statusLabel.setFont(getArabicFont());
        statusLabel.setForeground(Color.RED);
        statusLabel.setHorizontalAlignment(SwingConstants.CENTER);
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // العنوان
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات");
        titleLabel.setFont(getArabicFont().deriveFont(Font.BOLD, 18f));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        add(titleLabel, gbc);

        // خط فاصل
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        add(new JSeparator(), gbc);

        // حقل اسم المستخدم
        JLabel usernameLabel = new JLabel("اسم المستخدم:");
        usernameLabel.setFont(getArabicFont());
        usernameLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        gbc.gridx = 1; gbc.gridy = 2; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE;
        add(usernameLabel, gbc);

        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        add(usernameField, gbc);

        // حقل كلمة المرور
        JLabel passwordLabel = new JLabel("كلمة المرور:");
        passwordLabel.setFont(getArabicFont());
        passwordLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        gbc.gridx = 1; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        add(passwordLabel, gbc);

        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.HORIZONTAL;
        add(passwordField, gbc);

        // تسمية الحالة
        gbc.gridx = 0; gbc.gridy = 4; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        add(statusLabel, gbc);

        // الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(loginButton);
        buttonPanel.add(cancelButton);

        gbc.gridx = 0; gbc.gridy = 5; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        add(buttonPanel, gbc);
    }
    
    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // زر تسجيل الدخول
        loginButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                performLogin();
            }
        });
        
        // زر الإلغاء
        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
            }
        });
        
        // مفتاح Enter في حقل كلمة المرور
        passwordField.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                performLogin();
            }
        });
        
        // مفتاح Escape للإلغاء
        KeyStroke escapeKeyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_ESCAPE, 0, false);
        getRootPane().getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(escapeKeyStroke, "ESCAPE");
        getRootPane().getActionMap().put("ESCAPE", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
            }
        });
        
        // تعيين زر تسجيل الدخول كافتراضي
        getRootPane().setDefaultButton(loginButton);
    }
    
    /**
     * إعداد النافذة
     */
    private void setupDialog() {
        setTitle("تسجيل الدخول - نظام إدارة الشحنات");
        setModal(true);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
        setResizable(false);
        
        // تعيين اتجاه RTL للنافذة
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        pack();
        setLocationRelativeTo(null); // توسيط النافذة
        
        // تركيز على حقل اسم المستخدم
        SwingUtilities.invokeLater(() -> usernameField.requestFocus());
    }
    
    /**
     * تنفيذ عملية تسجيل الدخول
     */
    private void performLogin() {
        String username = usernameField.getText().trim();
        String password = new String(passwordField.getPassword());
        
        if (username.isEmpty()) {
            showStatus("يرجى إدخال اسم المستخدم", true);
            usernameField.requestFocus();
            return;
        }
        
        if (password.isEmpty()) {
            showStatus("يرجى إدخال كلمة المرور", true);
            passwordField.requestFocus();
            return;
        }
        
        // تعطيل الأزرار أثناء المعالجة
        setButtonsEnabled(false);
        showStatus("جاري التحقق...", false);
        
        // تنفيذ التحقق في thread منفصل
        SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                return authenticateUser(username, password);
            }
            
            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        loginSuccessful = true;
                        showStatus("تم تسجيل الدخول بنجاح", false);
                        
                        // إغلاق النافذة بعد تأخير قصير
                        Timer timer = new Timer(1000, e -> dispose());
                        timer.setRepeats(false);
                        timer.start();
                        
                    } else {
                        showStatus("اسم المستخدم أو كلمة المرور غير صحيحة", true);
                        passwordField.selectAll();
                        passwordField.requestFocus();
                    }
                } catch (Exception e) {
                    logger.error("خطأ في تسجيل الدخول", e);
                    showStatus("خطأ في الاتصال بقاعدة البيانات", true);
                } finally {
                    setButtonsEnabled(true);
                }
            }
        };
        
        worker.execute();
    }
    
    /**
     * التحقق من صحة بيانات المستخدم
     */
    private boolean authenticateUser(String username, String password) {
        String sql = "SELECT user_id, full_name, role_id, is_active, is_locked " +
                    "FROM users WHERE username = ? AND password = ?";
        
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            stmt.setString(2, password); // في التطبيق الحقيقي، يجب تشفير كلمة المرور
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    boolean isActive = rs.getInt("is_active") == 1;
                    boolean isLocked = rs.getInt("is_locked") == 1;
                    
                    if (!isActive) {
                        SwingUtilities.invokeLater(() -> 
                            showStatus("حساب المستخدم غير نشط", true));
                        return false;
                    }
                    
                    if (isLocked) {
                        SwingUtilities.invokeLater(() -> 
                            showStatus("حساب المستخدم مقفل", true));
                        return false;
                    }
                    
                    // تحديث آخر دخول
                    updateLastLogin(rs.getInt("user_id"));
                    
                    logger.info("تم تسجيل دخول المستخدم: {}", username);
                    return true;
                }
            }
            
        } catch (Exception e) {
            logger.error("خطأ في التحقق من بيانات المستخدم", e);
        }
        
        return false;
    }
    
    /**
     * تحديث آخر دخول للمستخدم
     */
    private void updateLastLogin(int userId) {
        String sql = "UPDATE users SET last_login = SYSDATE WHERE user_id = ?";
        
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            stmt.setInt(1, userId);
            stmt.executeUpdate();
            
        } catch (Exception e) {
            logger.warn("لم يتم تحديث آخر دخول للمستخدم", e);
        }
    }
    
    /**
     * عرض رسالة الحالة
     */
    private void showStatus(String message, boolean isError) {
        statusLabel.setText(message);
        statusLabel.setForeground(isError ? Color.RED : Color.BLUE);
    }
    
    /**
     * تفعيل/تعطيل الأزرار
     */
    private void setButtonsEnabled(boolean enabled) {
        loginButton.setEnabled(enabled);
        cancelButton.setEnabled(enabled);
        usernameField.setEnabled(enabled);
        passwordField.setEnabled(enabled);
    }
    
    /**
     * الحصول على الخط العربي
     */
    private Font getArabicFont() {
        String fontName = AppConfig.getInstance().getProperty("ui.font.arabic", "Tahoma");
        int fontSize = AppConfig.getInstance().getIntProperty("ui.font.size", 14);
        return new Font(fontName, Font.PLAIN, fontSize);
    }
    
    /**
     * التحقق من نجاح تسجيل الدخول
     */
    public boolean isLoginSuccessful() {
        return loginSuccessful;
    }
}
