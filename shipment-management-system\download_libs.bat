@echo off
echo Downloading Required Libraries...

if not exist "lib" mkdir lib

echo Downloading FlatLaf...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar' -OutFile 'lib\flatlaf-3.2.5.jar'"

echo Downloading FlatLaf Extras...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar' -OutFile 'lib\flatlaf-extras-3.2.5.jar'"

echo Downloading MigLayout...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/miglayout/miglayout-swing/11.3/miglayout-swing-11.3.jar' -OutFile 'lib\miglayout-swing-11.3.jar'"

echo Downloading Oracle JDBC...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/oracle/database/jdbc/ojdbc11/23.3.0.23.09/ojdbc11-23.3.0.23.09.jar' -OutFile 'lib\ojdbc11-23.3.0.23.09.jar'"

echo Downloading HikariCP...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar' -OutFile 'lib\HikariCP-5.1.0.jar'"

echo Downloading SLF4J API...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar' -OutFile 'lib\slf4j-api-2.0.9.jar'"

echo Downloading Logback Classic...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar' -OutFile 'lib\logback-classic-1.4.14.jar'"

echo Downloading Logback Core...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar' -OutFile 'lib\logback-core-1.4.14.jar'"

echo.
echo Checking downloaded libraries...
dir lib\*.jar

echo.
echo All libraries downloaded successfully!
pause
