package com.shipment.erp.util;

import com.shipment.erp.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.Locale;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

/**
 * مدير الرسائل مع دعم ترميز AR8MSWIN1256
 * Message Manager with AR8MSWIN1256 encoding support
 * 
 * <AUTHOR> ERP Team
 * @version 1.0.0
 */
public class MessageManager {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageManager.class);
    private static MessageManager instance;
    private ResourceBundle messages;
    private Locale currentLocale;
    
    private MessageManager() {
        // تعيين اللغة العربية كافتراضية
        currentLocale = new Locale("ar", "SA");
    }
    
    /**
     * الحصول على مثيل وحيد من مدير الرسائل
     */
    public static synchronized MessageManager getInstance() {
        if (instance == null) {
            instance = new MessageManager();
        }
        return instance;
    }
    
    /**
     * تحميل رسائل اللغة
     */
    public void loadMessages(Locale locale) {
        try {
            currentLocale = locale;
            
            // تحميل ملف الرسائل بترميز AR8MSWIN1256
            String baseName = "messages";
            String fileName = baseName + "_" + locale.getLanguage() + ".properties";
            
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName);
            if (inputStream != null) {
                // قراءة الملف بترميز AR8MSWIN1256
                Charset arabicCharset = AppConfig.getInstance().getArabicCharset();
                InputStreamReader reader = new InputStreamReader(inputStream, arabicCharset);
                messages = new PropertyResourceBundle(reader);
                
                logger.info("تم تحميل رسائل اللغة: {}", fileName);
            } else {
                logger.warn("لم يتم العثور على ملف الرسائل: {}", fileName);
                loadDefaultMessages();
            }
            
        } catch (IOException e) {
            logger.error("خطأ في تحميل رسائل اللغة", e);
            loadDefaultMessages();
        }
    }
    
    /**
     * تحميل الرسائل الافتراضية
     */
    private void loadDefaultMessages() {
        try {
            // استخدام ResourceBundle الافتراضي
            messages = ResourceBundle.getBundle("messages", currentLocale);
            logger.info("تم تحميل الرسائل الافتراضية");
        } catch (Exception e) {
            logger.error("فشل في تحميل الرسائل الافتراضية", e);
        }
    }
    
    /**
     * الحصول على رسالة
     */
    public String getMessage(String key) {
        try {
            if (messages != null && messages.containsKey(key)) {
                return messages.getString(key);
            } else {
                logger.warn("لم يتم العثور على الرسالة: {}", key);
                return key; // إرجاع المفتاح إذا لم توجد الرسالة
            }
        } catch (Exception e) {
            logger.error("خطأ في الحصول على الرسالة: {}", key, e);
            return key;
        }
    }
    
    /**
     * الحصول على رسالة مع معاملات
     */
    public String getMessage(String key, Object... params) {
        try {
            String message = getMessage(key);
            if (params != null && params.length > 0) {
                return MessageFormat.format(message, params);
            }
            return message;
        } catch (Exception e) {
            logger.error("خطأ في تنسيق الرسالة: {}", key, e);
            return key;
        }
    }
    
    /**
     * الحصول على رسالة مع قيمة افتراضية
     */
    public String getMessage(String key, String defaultValue) {
        try {
            if (messages != null && messages.containsKey(key)) {
                return messages.getString(key);
            } else {
                return defaultValue;
            }
        } catch (Exception e) {
            logger.error("خطأ في الحصول على الرسالة: {}", key, e);
            return defaultValue;
        }
    }
    
    /**
     * التحقق من وجود رسالة
     */
    public boolean hasMessage(String key) {
        try {
            return messages != null && messages.containsKey(key);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    public Locale getCurrentLocale() {
        return currentLocale;
    }
    
    /**
     * تغيير اللغة
     */
    public void changeLocale(Locale newLocale) {
        loadMessages(newLocale);
    }
    
    /**
     * الحصول على اتجاه النص (RTL/LTR)
     */
    public boolean isRightToLeft() {
        return currentLocale.getLanguage().equals("ar") || 
               currentLocale.getLanguage().equals("he") ||
               currentLocale.getLanguage().equals("fa");
    }
    
    /**
     * تنسيق رقم حسب اللغة الحالية
     */
    public String formatNumber(Number number) {
        try {
            java.text.NumberFormat formatter = java.text.NumberFormat.getInstance(currentLocale);
            return formatter.format(number);
        } catch (Exception e) {
            logger.error("خطأ في تنسيق الرقم", e);
            return number.toString();
        }
    }
    
    /**
     * تنسيق تاريخ حسب اللغة الحالية
     */
    public String formatDate(java.util.Date date) {
        try {
            java.text.DateFormat formatter = java.text.DateFormat.getDateInstance(
                java.text.DateFormat.MEDIUM, currentLocale);
            return formatter.format(date);
        } catch (Exception e) {
            logger.error("خطأ في تنسيق التاريخ", e);
            return date.toString();
        }
    }
    
    /**
     * تنسيق تاريخ ووقت حسب اللغة الحالية
     */
    public String formatDateTime(java.util.Date date) {
        try {
            java.text.DateFormat formatter = java.text.DateFormat.getDateTimeInstance(
                java.text.DateFormat.MEDIUM, java.text.DateFormat.MEDIUM, currentLocale);
            return formatter.format(date);
        } catch (Exception e) {
            logger.error("خطأ في تنسيق التاريخ والوقت", e);
            return date.toString();
        }
    }
    
    // رسائل شائعة
    public static final String MSG_SAVE_SUCCESS = "msg.save.success";
    public static final String MSG_SAVE_ERROR = "msg.save.error";
    public static final String MSG_DELETE_CONFIRM = "msg.delete.confirm";
    public static final String MSG_DELETE_SUCCESS = "msg.delete.success";
    public static final String MSG_DELETE_ERROR = "msg.delete.error";
    public static final String MSG_VALIDATION_REQUIRED = "msg.validation.required";
    public static final String MSG_VALIDATION_INVALID = "msg.validation.invalid";
    public static final String MSG_DATABASE_ERROR = "msg.database.error";
    public static final String MSG_CONNECTION_ERROR = "msg.connection.error";
    
    // تسميات شائعة
    public static final String LABEL_SAVE = "label.save";
    public static final String LABEL_CANCEL = "label.cancel";
    public static final String LABEL_EDIT = "label.edit";
    public static final String LABEL_DELETE = "label.delete";
    public static final String LABEL_SEARCH = "label.search";
    public static final String LABEL_PRINT = "label.print";
    public static final String LABEL_EXPORT = "label.export";
    public static final String LABEL_IMPORT = "label.import";
    public static final String LABEL_REFRESH = "label.refresh";
    public static final String LABEL_CLOSE = "label.close";
    public static final String LABEL_NEW = "label.new";
}
