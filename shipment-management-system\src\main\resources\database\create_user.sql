-- إنشاء مستخدم قاعدة البيانات لنظام إدارة الشحنات
-- Create Database User for Shipment Management System

-- Connect as SYSTEM or DBA user first
-- sqlplus system/password@localhost:1521/XE

-- Create tablespace for the application
CREATE TABLESPACE SHIP_ERP_DATA
DATAFILE 'ship_erp_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G
EXTENT MANAGEMENT LOCAL
SEGMENT SPACE MANAGEMENT AUTO;

-- Create temporary tablespace
CREATE TEMPORARY TABLESPACE SHIP_ERP_TEMP
TEMPFILE 'ship_erp_temp.dbf' SIZE 50M
AUTOEXTEND ON NEXT 5M MAXSIZE 500M;

-- Create user
CREATE USER ship_erp IDENTIFIED BY ys123
DEFAULT TABLESPACE SHIP_ERP_DATA
TEMPORARY TABLESPACE SHIP_ERP_TEMP
QUOTA UNLIMITED ON SHIP_ERP_DATA;

-- Grant necessary privileges
GRANT CONNECT TO ship_erp;
GRANT RESOURCE TO ship_erp;
GRANT CREATE SESSION TO ship_erp;
GRANT CREATE TABLE TO ship_erp;
GRANT CREATE VIEW TO ship_erp;
GRANT CREATE SEQUENCE TO ship_erp;
GRANT CREATE PROCEDURE TO ship_erp;
GRANT CREATE TRIGGER TO ship_erp;
GRANT CREATE SYNONYM TO ship_erp;

-- Additional privileges for advanced features
GRANT CREATE JOB TO ship_erp;
GRANT CREATE TYPE TO ship_erp;
GRANT CREATE MATERIALIZED VIEW TO ship_erp;

-- Grant system privileges for backup and maintenance
GRANT EXP_FULL_DATABASE TO ship_erp;
GRANT IMP_FULL_DATABASE TO ship_erp;

COMMIT;

-- Verify user creation
SELECT username, default_tablespace, temporary_tablespace, account_status
FROM dba_users 
WHERE username = 'SHIP_ERP';

-- Show granted privileges
SELECT grantee, privilege, admin_option
FROM dba_sys_privs 
WHERE grantee = 'SHIP_ERP'
ORDER BY privilege;
