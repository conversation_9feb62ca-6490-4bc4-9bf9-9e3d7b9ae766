@echo off
chcp 65001 > nul
echo ========================================
echo نظام إدارة الشحنات - إنشاء الجداول
echo Shipment Management System - Create Tables
echo ========================================
echo.

set /p ORACLE_HOST="عنوان الخادم (localhost): "
if "%ORACLE_HOST%"=="" set ORACLE_HOST=localhost

set /p ORACLE_PORT="رقم المنفذ (1521): "
if "%ORACLE_PORT%"=="" set ORACLE_PORT=1521

set /p ORACLE_SID="اسم قاعدة البيانات (ORCL): "
if "%ORACLE_SID%"=="" set ORACLE_SID=ORCL

echo.
echo جاري إنشاء الجداول...
echo Creating tables...
echo.

sqlplus ship_erp/ys123@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SID% @src/main/resources/database/create_tables.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم إنشاء الجداول بنجاح!
    echo ✓ Tables created successfully!
    echo.
    
    echo جاري التحقق من الجداول المنشأة...
    echo Checking created tables...
    
    echo SELECT table_name FROM user_tables ORDER BY table_name; > check_tables.sql
    echo EXIT; >> check_tables.sql
    
    sqlplus ship_erp/ys123@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SID% @check_tables.sql
    
    del check_tables.sql
    
    echo.
    echo النظام جاهز للاستخدام!
    echo System is ready to use!
    
) else (
    echo.
    echo ✗ فشل في إنشاء الجداول
    echo ✗ Failed to create tables
    echo يرجى التحقق من الأخطاء أعلاه
    echo Please check the errors above
)

echo.
pause
