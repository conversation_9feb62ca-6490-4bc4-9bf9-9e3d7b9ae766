@echo off
echo ========================================
echo Shipment Management System - Database Setup
echo ========================================
echo.

set /p SYSTEM_PASSWORD="Enter SYSTEM password: "

echo Creating database user...

echo DROP USER ship_erp CASCADE; > setup.sql
echo CREATE USER ship_erp IDENTIFIED BY ys123; >> setup.sql
echo GRANT CONNECT TO ship_erp; >> setup.sql
echo GRANT RESOURCE TO ship_erp; >> setup.sql
echo GRANT CREATE SESSION TO ship_erp; >> setup.sql
echo GRANT CREATE TABLE TO ship_erp; >> setup.sql
echo GRANT CREATE VIEW TO ship_erp; >> setup.sql
echo GRANT CREATE SEQUENCE TO ship_erp; >> setup.sql
echo GRANT CREATE PROCEDURE TO ship_erp; >> setup.sql
echo GRANT CREATE TRIGGER TO ship_erp; >> setup.sql
echo GRANT UNLIMITED TABLESPACE TO ship_erp; >> setup.sql
echo SELECT username, account_status FROM dba_users WHERE username = 'SHIP_ERP'; >> setup.sql
echo EXIT; >> setup.sql

sqlplus system/%SYSTEM_PASSWORD%@localhost:1521/ORCL @setup.sql

if %ERRORLEVEL% EQU 0 (
    echo User created successfully!
    echo Testing connection...
    
    echo SELECT 'Connection successful!' FROM dual; > test.sql
    echo EXIT; >> test.sql
    
    sqlplus ship_erp/ys123@localhost:1521/ORCL @test.sql
    
    if %ERRORLEVEL% EQU 0 (
        echo Connection test passed!
    ) else (
        echo Connection test failed!
    )
    
    del test.sql
) else (
    echo Failed to create user!
)

del setup.sql
pause
