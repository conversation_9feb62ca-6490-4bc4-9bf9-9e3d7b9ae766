package com.shipment.erp.ui;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.Font;

import javax.swing.BorderFactory;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shipment.erp.config.AppConfig;
import com.shipment.erp.util.MessageManager;

/**
 * الواجهة الرئيسية للتطبيق مع دعم RTL والترميز العربي
 * Main Application Frame with RTL support and Arabic encoding
 * 
 * <AUTHOR> ERP Team
 * @version 1.0.0
 */
public class MainFrame extends JFrame {
    
    private static final Logger logger = LoggerFactory.getLogger(MainFrame.class);
    
    private JTree systemTree;
    private JTabbedPane mainTabbedPane;
    private JLabel statusLabel;
    private MessageManager messageManager;
    
    public MainFrame() {
        messageManager = MessageManager.getInstance();
        
        initializeComponents();
        setupLayout();
        setupMenuBar();
        setupEventHandlers();
        setupFrame();
        
        logger.info("تم تشغيل الواجهة الرئيسية");
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // تعيين اتجاه RTL
        ComponentOrientation rtl = ComponentOrientation.RIGHT_TO_LEFT;
        
        // شجرة الأنظمة
        systemTree = createSystemTree();
        systemTree.setComponentOrientation(rtl);
        systemTree.setFont(getArabicFont());
        
        // لوحة التبويبات الرئيسية
        mainTabbedPane = new JTabbedPane();
        mainTabbedPane.setComponentOrientation(rtl);
        mainTabbedPane.setFont(getArabicFont());
        mainTabbedPane.setTabPlacement(JTabbedPane.TOP);
        
        // إضافة تبويب ترحيبي
        addWelcomeTab();
        
        // شريط الحالة
        statusLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات");
        statusLabel.setComponentOrientation(rtl);
        statusLabel.setFont(getArabicFont());
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
    }
    
    /**
     * إنشاء شجرة الأنظمة
     */
    private JTree createSystemTree() {
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("الأنظمة الرئيسية");
        
        // نظام الإعدادات العامة
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("نظام الإعدادات العامة");
        settingsNode.add(new DefaultMutableTreeNode("المتغيرات العامة للبرنامج"));
        settingsNode.add(new DefaultMutableTreeNode("إعداد السنة المالية"));
        settingsNode.add(new DefaultMutableTreeNode("تهيئة العملات"));
        settingsNode.add(new DefaultMutableTreeNode("بيانات الشركة"));
        settingsNode.add(new DefaultMutableTreeNode("بيانات الفروع"));
        settingsNode.add(new DefaultMutableTreeNode("بيانات المستخدمين"));
        settingsNode.add(new DefaultMutableTreeNode("صلاحيات المستخدمين"));
        settingsNode.add(new DefaultMutableTreeNode("فتح سنة جديدة"));
        root.add(settingsNode);
        
        // الأنظمة الأخرى (قيد التطوير)
        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("نظام إدارة الأصناف (قيد التطوير)");
        root.add(itemsNode);
        
        DefaultMutableTreeNode suppliersNode = new DefaultMutableTreeNode("نظام إدارة الموردين (قيد التطوير)");
        root.add(suppliersNode);
        
        DefaultMutableTreeNode shipmentsNode = new DefaultMutableTreeNode("نظام إدارة متابعة وتتبع الشحنات (قيد التطوير)");
        root.add(shipmentsNode);
        
        DefaultMutableTreeNode customsNode = new DefaultMutableTreeNode("نظام الإدخالات الجمركية (قيد التطوير)");
        root.add(customsNode);
        
        DefaultMutableTreeNode costsNode = new DefaultMutableTreeNode("نظام التكاليف (قيد التطوير)");
        root.add(costsNode);
        
        DefaultTreeModel treeModel = new DefaultTreeModel(root);
        JTree tree = new JTree(treeModel);
        tree.setRootVisible(true);
        tree.setShowsRootHandles(true);
        
        // توسيع العقد الرئيسية
        tree.expandRow(0);
        tree.expandRow(1);
        
        return tree;
    }
    
    /**
     * إضافة تبويب الترحيب
     */
    private void addWelcomeTab() {
        JPanel welcomePanel = new JPanel(new BorderLayout());
        welcomePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // عنوان الترحيب
        JLabel titleLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات");
        titleLabel.setFont(getArabicFont().deriveFont(Font.BOLD, 24f));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        // معلومات النظام
        JTextArea infoArea = new JTextArea();
        infoArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        infoArea.setFont(getArabicFont());
        infoArea.setEditable(false);
        infoArea.setOpaque(false);
        infoArea.setText(
            "نظام إدارة الشحنات المتكامل\n\n" +
            "الإصدار: 1.0.0\n" +
            "تاريخ الإصدار: " + java.time.LocalDate.now() + "\n\n" +
            "الميزات المتاحة:\n" +
            "• نظام الإعدادات العامة (متاح)\n" +
            "• نظام إدارة الأصناف (قيد التطوير)\n" +
            "• نظام إدارة الموردين (قيد التطوير)\n" +
            "• نظام إدارة متابعة وتتبع الشحنات (قيد التطوير)\n" +
            "• نظام الإدخالات الجمركية (قيد التطوير)\n" +
            "• نظام التكاليف (قيد التطوير)\n\n" +
            "للبدء، اختر أحد الأنظمة من الشجرة على اليمين"
        );
        
        JPanel centerPanel = new JPanel(new BorderLayout());
        centerPanel.add(titleLabel, BorderLayout.NORTH);
        centerPanel.add(new JSeparator(), BorderLayout.CENTER);

        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.add(infoArea, BorderLayout.CENTER);
        centerPanel.add(infoPanel, BorderLayout.SOUTH);

        welcomePanel.add(centerPanel, BorderLayout.CENTER);
        
        mainTabbedPane.addTab("الصفحة الرئيسية", welcomePanel);
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // الشجرة في الجانب الأيمن
        JScrollPane treeScrollPane = new JScrollPane(systemTree);
        treeScrollPane.setPreferredSize(new Dimension(300, 0));
        treeScrollPane.setBorder(BorderFactory.createTitledBorder("قائمة الأنظمة"));
        
        // لوحة التبويبات في الوسط
        JScrollPane mainScrollPane = new JScrollPane(mainTabbedPane);
        
        // تقسيم الشاشة
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, 
                                             treeScrollPane, mainScrollPane);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.0);
        
        add(splitPane, BorderLayout.CENTER);
        add(statusLabel, BorderLayout.SOUTH);
    }
    
    /**
     * إعداد شريط القوائم
     */
    private void setupMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // قائمة ملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        fileMenu.setFont(getArabicFont());
        
        JMenuItem exitItem = new JMenuItem("خروج");
        exitItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        exitItem.setFont(getArabicFont());
        exitItem.addActionListener(e -> System.exit(0));
        
        fileMenu.add(exitItem);
        
        // قائمة عرض
        JMenu viewMenu = new JMenu("عرض");
        viewMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        viewMenu.setFont(getArabicFont());
        
        JMenuItem refreshItem = new JMenuItem("تحديث");
        refreshItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        refreshItem.setFont(getArabicFont());
        refreshItem.addActionListener(e -> refreshView());
        
        viewMenu.add(refreshItem);
        
        // قائمة مساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        helpMenu.setFont(getArabicFont());
        
        JMenuItem aboutItem = new JMenuItem("حول البرنامج");
        aboutItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        aboutItem.setFont(getArabicFont());
        aboutItem.addActionListener(e -> showAboutDialog());
        
        helpMenu.add(aboutItem);
        
        menuBar.add(fileMenu);
        menuBar.add(viewMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // معالج النقر على شجرة الأنظمة
        systemTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = 
                (DefaultMutableTreeNode) systemTree.getLastSelectedPathComponent();
            
            if (selectedNode != null) {
                String nodeName = selectedNode.toString();
                handleSystemSelection(nodeName);
            }
        });
    }
    
    /**
     * معالجة اختيار نظام من الشجرة
     */
    private void handleSystemSelection(String systemName) {
        updateStatus("تم اختيار: " + systemName);
        
        // التحقق من وجود التبويب مسبقاً
        for (int i = 0; i < mainTabbedPane.getTabCount(); i++) {
            if (mainTabbedPane.getTitleAt(i).equals(systemName)) {
                mainTabbedPane.setSelectedIndex(i);
                return;
            }
        }
        
        // إنشاء تبويب جديد حسب النظام المختار
        JPanel systemPanel = createSystemPanel(systemName);
        if (systemPanel != null) {
            mainTabbedPane.addTab(systemName, systemPanel);
            mainTabbedPane.setSelectedIndex(mainTabbedPane.getTabCount() - 1);
        }
    }
    
    /**
     * إنشاء لوحة النظام
     */
    private JPanel createSystemPanel(String systemName) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        if (systemName.contains("قيد التطوير")) {
            // رسالة للأنظمة قيد التطوير
            JLabel messageLabel = new JLabel("هذا النظام قيد التطوير");
            messageLabel.setFont(getArabicFont().deriveFont(Font.BOLD, 18f));
            messageLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            messageLabel.setHorizontalAlignment(SwingConstants.CENTER);
            
            panel.add(messageLabel, BorderLayout.CENTER);
            
        } else {
            // الأنظمة المتاحة
            JLabel titleLabel = new JLabel(systemName);
            titleLabel.setFont(getArabicFont().deriveFont(Font.BOLD, 16f));
            titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
            
            JTextArea descArea = new JTextArea("سيتم إضافة محتوى هذا النظام قريباً...");
            descArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            descArea.setFont(getArabicFont());
            descArea.setEditable(false);
            descArea.setOpaque(false);
            
            panel.add(titleLabel, BorderLayout.NORTH);
            panel.add(descArea, BorderLayout.CENTER);
        }
        
        return panel;
    }
    
    /**
     * إعداد النافذة الرئيسية
     */
    private void setupFrame() {
        setTitle("نظام إدارة الشحنات - الإصدار 1.0.0");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تعيين حجم النافذة
        boolean maximized = AppConfig.getInstance().getBooleanProperty("ui.window.maximized", true);
        if (maximized) {
            setExtendedState(JFrame.MAXIMIZED_BOTH);
        } else {
            setSize(1200, 800);
            setLocationRelativeTo(null);
        }
        
        // تعيين أيقونة التطبيق (إذا كانت متوفرة)
        try {
            // يمكن إضافة أيقونة هنا
            // setIconImage(ImageIO.read(getClass().getResource("/icon.png")));
        } catch (Exception e) {
            logger.debug("لم يتم العثور على أيقونة التطبيق");
        }
    }
    
    /**
     * تحديث العرض
     */
    private void refreshView() {
        updateStatus("تم تحديث العرض");
        repaint();
    }
    
    /**
     * عرض نافذة حول البرنامج
     */
    private void showAboutDialog() {
        String message = "نظام إدارة الشحنات المتكامل\n\n" +
                        "الإصدار: 1.0.0\n" +
                        "تاريخ الإصدار: " + java.time.LocalDate.now() + "\n\n" +
                        "تم تطويره باستخدام Java مع دعم كامل للغة العربية\n" +
                        "وترميز AR8MSWIN1256";
        
        JOptionPane.showMessageDialog(this, message, "حول البرنامج", 
                                    JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * تحديث شريط الحالة
     */
    private void updateStatus(String message) {
        statusLabel.setText(message + " - " + java.time.LocalTime.now().toString());
    }
    
    /**
     * الحصول على الخط العربي
     */
    private Font getArabicFont() {
        String fontName = AppConfig.getInstance().getProperty("ui.font.arabic", "Tahoma");
        int fontSize = AppConfig.getInstance().getIntProperty("ui.font.size", 14);
        return new Font(fontName, Font.PLAIN, fontSize);
    }
}
