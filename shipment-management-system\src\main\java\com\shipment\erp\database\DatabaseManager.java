package com.shipment.erp.database;

import com.shipment.erp.config.AppConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

/**
 * مدير قاعدة البيانات مع دعم ترميز AR8MSWIN1256
 * Database Manager with AR8MSWIN1256 encoding support
 * 
 * <AUTHOR> ERP Team
 * @version 1.0.0
 */
public class DatabaseManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseManager.class);
    private static DatabaseManager instance;
    private HikariDataSource dataSource;
    private AppConfig config;
    
    private DatabaseManager() {
        config = AppConfig.getInstance();
        initializeDataSource();
    }
    
    /**
     * الحصول على مثيل وحيد من مدير قاعدة البيانات
     */
    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }
    
    /**
     * تهيئة مصدر البيانات مع إعدادات الترميز العربي
     */
    private void initializeDataSource() {
        try {
            HikariConfig hikariConfig = new HikariConfig();
            
            // إعدادات الاتصال الأساسية
            hikariConfig.setJdbcUrl(config.getDatabaseUrl());
            hikariConfig.setUsername(config.getProperty("db.username"));
            hikariConfig.setPassword(config.getProperty("db.password"));
            hikariConfig.setDriverClassName(config.getProperty("db.driver"));
            
            // إعدادات pool الاتصالات
            hikariConfig.setMaximumPoolSize(config.getIntProperty("db.pool.maximum", 20));
            hikariConfig.setMinimumIdle(config.getIntProperty("db.pool.minimum", 5));
            hikariConfig.setConnectionTimeout(config.getIntProperty("db.pool.timeout", 30000));
            hikariConfig.setIdleTimeout(600000); // 10 دقائق
            hikariConfig.setMaxLifetime(1800000); // 30 دقيقة
            
            // إعدادات خاصة بـ Oracle والترميز العربي
            Properties dataSourceProperties = new Properties();
            
            // إعدادات الترميز العربي
            dataSourceProperties.setProperty("oracle.jdbc.defaultNChar", "true");
            dataSourceProperties.setProperty("oracle.jdbc.convertNcharLiterals", "true");
            dataSourceProperties.setProperty("oracle.jdbc.defaultRowPrefetch", "20");
            
            // إعدادات NLS للدعم العربي
            dataSourceProperties.setProperty("oracle.sessionTimeZone", "Asia/Riyadh");
            
            // إعدادات الأداء
            dataSourceProperties.setProperty("oracle.net.CONNECT_TIMEOUT", "10000");
            dataSourceProperties.setProperty("oracle.jdbc.ReadTimeout", "30000");
            dataSourceProperties.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            
            hikariConfig.setDataSourceProperties(dataSourceProperties);
            
            // إعدادات إضافية للـ pool
            hikariConfig.setPoolName("ShipmentERP-Pool");
            hikariConfig.setLeakDetectionThreshold(60000); // دقيقة واحدة
            
            // SQL للتحقق من صحة الاتصال
            hikariConfig.setConnectionTestQuery("SELECT 1 FROM DUAL");
            
            dataSource = new HikariDataSource(hikariConfig);
            
            logger.info("تم تهيئة مصدر البيانات بنجاح مع دعم الترميز العربي");
            
        } catch (Exception e) {
            logger.error("خطأ في تهيئة مصدر البيانات", e);
            throw new RuntimeException("فشل في تهيئة قاعدة البيانات", e);
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("مصدر البيانات غير مهيأ");
        }
        
        Connection connection = dataSource.getConnection();
        
        // تعيين إعدادات الجلسة للدعم العربي
        setupSessionForArabic(connection);
        
        return connection;
    }
    
    /**
     * تعيين إعدادات الجلسة للدعم العربي
     */
    private void setupSessionForArabic(Connection connection) {
        try (Statement stmt = connection.createStatement()) {
            // تعيين اللغة العربية
            stmt.execute("ALTER SESSION SET NLS_LANGUAGE='ARABIC'");
            stmt.execute("ALTER SESSION SET NLS_TERRITORY='SAUDI ARABIA'");
            
            // تعيين تنسيق التاريخ
            stmt.execute("ALTER SESSION SET NLS_DATE_FORMAT='DD/MM/YYYY'");
            stmt.execute("ALTER SESSION SET NLS_TIMESTAMP_FORMAT='DD/MM/YYYY HH24:MI:SS'");
            
            // تعيين المنطقة الزمنية
            stmt.execute("ALTER SESSION SET TIME_ZONE='Asia/Riyadh'");
            
            logger.debug("تم تعيين إعدادات الجلسة للدعم العربي");
            
        } catch (SQLException e) {
            logger.warn("تحذير: لم يتم تعيين بعض إعدادات الجلسة العربية", e);
        }
    }
    
    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public boolean testConnection() {
        try (Connection connection = getConnection()) {
            if (connection != null && !connection.isClosed()) {
                // اختبار بسيط للتأكد من الاتصال
                try (PreparedStatement stmt = connection.prepareStatement("SELECT 1 FROM DUAL")) {
                    try (ResultSet rs = stmt.executeQuery()) {
                        boolean hasResult = rs.next();
                        logger.info("اختبار الاتصال بقاعدة البيانات: نجح");
                        return hasResult;
                    }
                }
            }
        } catch (SQLException e) {
            logger.error("فشل في اختبار الاتصال بقاعدة البيانات", e);
        }
        return false;
    }
    
    /**
     * اختبار الترميز العربي
     */
    public boolean testArabicEncoding() {
        String testQuery = "SELECT 'اختبار الترميز العربي' as test_text FROM DUAL";
        
        try (Connection connection = getConnection();
             PreparedStatement stmt = connection.prepareStatement(testQuery);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                String result = rs.getString("test_text");
                boolean isValid = result != null && result.contains("اختبار");
                
                if (isValid) {
                    logger.info("اختبار الترميز العربي: نجح - {}", result);
                } else {
                    logger.warn("اختبار الترميز العربي: فشل - {}", result);
                }
                
                return isValid;
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في اختبار الترميز العربي", e);
        }
        
        return false;
    }
    
    /**
     * الحصول على معلومات قاعدة البيانات
     */
    public String getDatabaseInfo() {
        StringBuilder info = new StringBuilder();
        
        try (Connection connection = getConnection();
             Statement stmt = connection.createStatement()) {
            
            // معلومات الإصدار
            try (ResultSet rs = stmt.executeQuery("SELECT * FROM v$version WHERE banner LIKE 'Oracle%'")) {
                if (rs.next()) {
                    info.append("إصدار Oracle: ").append(rs.getString("banner")).append("\n");
                }
            }
            
            // معلومات الترميز
            try (ResultSet rs = stmt.executeQuery(
                "SELECT parameter, value FROM nls_database_parameters " +
                "WHERE parameter IN ('NLS_CHARACTERSET', 'NLS_NCHAR_CHARACTERSET', 'NLS_LANGUAGE', 'NLS_TERRITORY')")) {
                
                while (rs.next()) {
                    info.append(rs.getString("parameter")).append(": ")
                        .append(rs.getString("value")).append("\n");
                }
            }
            
            // معلومات المستخدم الحالي
            try (ResultSet rs = stmt.executeQuery("SELECT USER, SYSDATE FROM DUAL")) {
                if (rs.next()) {
                    info.append("المستخدم الحالي: ").append(rs.getString("USER")).append("\n");
                    info.append("التاريخ والوقت: ").append(rs.getTimestamp("SYSDATE")).append("\n");
                }
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في الحصول على معلومات قاعدة البيانات", e);
            info.append("خطأ في الحصول على المعلومات: ").append(e.getMessage());
        }
        
        return info.toString();
    }
    
    /**
     * إغلاق مصدر البيانات
     */
    public void shutdown() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("تم إغلاق مصدر البيانات");
        }
    }
    
    /**
     * الحصول على مصدر البيانات
     */
    public DataSource getDataSource() {
        return dataSource;
    }
    
    /**
     * تنفيذ استعلام مع معاملات
     */
    public ResultSet executeQuery(String sql, Object... parameters) throws SQLException {
        Connection connection = getConnection();
        PreparedStatement stmt = connection.prepareStatement(sql);
        
        // تعيين المعاملات
        for (int i = 0; i < parameters.length; i++) {
            stmt.setObject(i + 1, parameters[i]);
        }
        
        return stmt.executeQuery();
    }
    
    /**
     * تنفيذ أمر تحديث مع معاملات
     */
    public int executeUpdate(String sql, Object... parameters) throws SQLException {
        try (Connection connection = getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            // تعيين المعاملات
            for (int i = 0; i < parameters.length; i++) {
                stmt.setObject(i + 1, parameters[i]);
            }
            
            return stmt.executeUpdate();
        }
    }
}
