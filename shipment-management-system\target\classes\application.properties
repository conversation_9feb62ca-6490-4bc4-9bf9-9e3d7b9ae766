# نظام إدارة الشحنات - إعدادات التطبيق
# Shipment Management System - Application Configuration

# Database Configuration - Oracle
db.url=*************************************
db.username=ship_erp
db.password=ys123
db.driver=oracle.jdbc.OracleDriver

# Oracle Character Set Configuration
db.charset=AR8MSWIN1256
db.nls.language=ARABIC
db.nls.territory=SAUDI ARABIA
db.nls.date.format=DD/MM/YYYY
db.nls.timestamp.format=DD/MM/YYYY HH24:MI:SS

# Connection Pool Settings
db.pool.maximum=20
db.pool.minimum=5
db.pool.timeout=30000

# Application Settings
app.name=نظام إدارة الشحنات
app.version=1.0.0
app.company=شركة إدارة الشحنات
app.locale=ar_SA
app.rtl=true

# UI Settings
ui.theme=FlatLaf Dark
ui.font.arabic=Tahoma
ui.font.size=14
ui.window.maximized=true

# Logging Configuration
logging.level=INFO
logging.file=logs/shipment-system.log

# Report Settings
reports.path=reports/
reports.logo=resources/images/logo.png

# Backup Settings
backup.path=backup/
backup.auto=true
backup.interval=24

# Security Settings
security.session.timeout=30
security.password.min.length=6
security.login.attempts=3
