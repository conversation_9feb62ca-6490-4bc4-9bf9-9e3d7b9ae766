@echo off
chcp 65001 > nul
echo ========================================
echo نظام إدارة الشحنات - اختبار الاتصال بقاعدة البيانات
echo Shipment Management System - Database Connection Test
echo ========================================
echo.

set /p ORACLE_HOST="عنوان الخادم (localhost): "
if "%ORACLE_HOST%"=="" set ORACLE_HOST=localhost

set /p ORACLE_PORT="رقم المنفذ (1521): "
if "%ORACLE_PORT%"=="" set ORACLE_PORT=1521

set /p ORACLE_SID="اسم قاعدة البيانات (ORCL): "
if "%ORACLE_SID%"=="" set ORACLE_SID=ORCL

echo.
echo جاري اختبار الاتصال بمستخدم ship_erp...
echo Testing connection with ship_erp user...
echo.

echo -- اختبار الاتصال > test_full_connection.sql
echo SELECT 'اتصال ناجح - Connection Successful!' as status FROM dual; >> test_full_connection.sql
echo. >> test_full_connection.sql
echo SELECT 'معلومات المستخدم - User Information:' as info FROM dual; >> test_full_connection.sql
echo SELECT USER as current_user, SYSDATE as current_date FROM dual; >> test_full_connection.sql
echo. >> test_full_connection.sql
echo SELECT 'الجداول المتاحة - Available Tables:' as info FROM dual; >> test_full_connection.sql
echo SELECT table_name FROM user_tables ORDER BY table_name; >> test_full_connection.sql
echo. >> test_full_connection.sql
echo SELECT 'عدد الجداول - Table Count:' as info FROM dual; >> test_full_connection.sql
echo SELECT COUNT(*) as table_count FROM user_tables; >> test_full_connection.sql
echo. >> test_full_connection.sql
echo EXIT; >> test_full_connection.sql

sqlplus ship_erp/ys123@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SID% @test_full_connection.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ الاتصال ناجح!
    echo ✓ Connection successful!
    echo.
    echo يمكنك الآن تشغيل التطبيق
    echo You can now run the application
) else (
    echo.
    echo ✗ فشل في الاتصال
    echo ✗ Connection failed
    echo.
    echo تأكد من:
    echo Please check:
    echo 1. تشغيل خدمة Oracle
    echo    Oracle service is running
    echo 2. صحة بيانات الاتصال
    echo    Connection details are correct
    echo 3. وجود المستخدم ship_erp
    echo    User ship_erp exists
)

del test_full_connection.sql

echo.
pause
