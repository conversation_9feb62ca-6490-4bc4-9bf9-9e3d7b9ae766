DROP USER ship_erp CASCADE; 
CREATE USER ship_erp IDENTIFIED BY ys123; 
GRANT CONNECT TO ship_erp; 
GRANT RESOURCE TO ship_erp; 
GRANT CREATE SESSION TO ship_erp; 
GRANT CREATE TABLE TO ship_erp; 
GRANT CREATE VIEW TO ship_erp; 
GRANT CREATE SEQUENCE TO ship_erp; 
GRANT CREATE PROCEDURE TO ship_erp; 
GRANT CREATE TRIGGER TO ship_erp; 
GRANT UNLIMITED TABLESPACE TO ship_erp; 
SELECT username, account_status FROM dba_users WHERE username = 'SHIP_ERP'; 
EXIT; 
