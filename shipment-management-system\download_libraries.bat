@echo off
chcp 65001 > nul
title تحميل المكتبات - Download Libraries

echo ========================================
echo تحميل المكتبات المطلوبة
echo Downloading Required Libraries
echo ========================================
echo.

if not exist "lib" mkdir lib

echo جاري تحميل المكتبات...
echo Downloading libraries...
echo.

echo 1. تحميل FlatLaf...
echo    Downloading FlatLaf...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar' -OutFile 'lib\flatlaf-3.2.5.jar'"

echo 2. تحميل FlatLaf Extras...
echo    Downloading FlatLaf Extras...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar' -OutFile 'lib\flatlaf-extras-3.2.5.jar'"

echo 3. تحميل MigLayout...
echo    Downloading MigLayout...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/miglayout/miglayout-swing/11.3/miglayout-swing-11.3.jar' -OutFile 'lib\miglayout-swing-11.3.jar'"

echo 4. تحميل Oracle JDBC...
echo    Downloading Oracle JDBC...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/oracle/database/jdbc/ojdbc11/23.3.0.23.09/ojdbc11-23.3.0.23.09.jar' -OutFile 'lib\ojdbc11-23.3.0.23.09.jar'"

echo 5. تحميل HikariCP...
echo    Downloading HikariCP...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar' -OutFile 'lib\HikariCP-5.1.0.jar'"

echo 6. تحميل SLF4J API...
echo    Downloading SLF4J API...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar' -OutFile 'lib\slf4j-api-2.0.9.jar'"

echo 7. تحميل Logback Classic...
echo    Downloading Logback Classic...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar' -OutFile 'lib\logback-classic-1.4.14.jar'"

echo 8. تحميل Logback Core...
echo    Downloading Logback Core...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar' -OutFile 'lib\logback-core-1.4.14.jar'"

echo.
echo التحقق من المكتبات المحملة...
echo Checking downloaded libraries...
echo.

for %%f in (lib\*.jar) do (
    echo ✓ %%f
)

echo.
echo ✓ تم تحميل جميع المكتبات بنجاح!
echo ✓ All libraries downloaded successfully!
echo.

pause
