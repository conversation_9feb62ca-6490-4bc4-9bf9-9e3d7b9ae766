package com.shipment.erp;

import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatLightLaf;
import com.shipment.erp.config.AppConfig;
import com.shipment.erp.database.DatabaseManager;
import com.shipment.erp.ui.MainFrame;
import com.shipment.erp.ui.login.LoginDialog;
import com.shipment.erp.util.MessageManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.util.Locale;

/**
 * نظام إدارة الشحنات - التطبيق الرئيسي
 * Shipment Management System - Main Application
 * 
 * <AUTHOR> ERP Team
 * @version 1.0.0
 */
public class ShipmentManagementApp {
    
    private static final Logger logger = LoggerFactory.getLogger(ShipmentManagementApp.class);
    
    public static void main(String[] args) {
        // تعيين خصائص النظام للدعم العربي
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");
        
        // تشغيل التطبيق في Event Dispatch Thread
        SwingUtilities.invokeLater(() -> {
            try {
                initializeApplication();
            } catch (Exception e) {
                logger.error("خطأ في تشغيل التطبيق", e);
                showErrorDialog("خطأ في تشغيل التطبيق: " + e.getMessage());
                System.exit(1);
            }
        });
    }
    
    /**
     * تهيئة التطبيق
     */
    private static void initializeApplication() {
        logger.info("بدء تشغيل نظام إدارة الشحنات...");
        
        // تحميل الإعدادات
        AppConfig.getInstance().loadConfiguration();
        
        // تعيين اللغة والمنطقة
        setupLocale();
        
        // تعيين مظهر التطبيق
        setupLookAndFeel();
        
        // تهيئة قاعدة البيانات
        if (!initializeDatabase()) {
            showErrorDialog("فشل في الاتصال بقاعدة البيانات");
            System.exit(1);
        }
        
        // عرض شاشة تسجيل الدخول
        showLoginDialog();
        
        logger.info("تم تشغيل النظام بنجاح");
    }
    
    /**
     * تعيين اللغة والمنطقة
     */
    private static void setupLocale() {
        Locale arabicLocale = new Locale("ar", "SA");
        Locale.setDefault(arabicLocale);
        
        // تحميل رسائل اللغة العربية
        MessageManager.getInstance().loadMessages(arabicLocale);
        
        logger.info("تم تعيين اللغة العربية");
    }
    
    /**
     * تعيين مظهر التطبيق
     */
    private static void setupLookAndFeel() {
        try {
            // استخدام FlatLaf للحصول على مظهر عصري
            String theme = AppConfig.getInstance().getProperty("ui.theme", "FlatLaf Light");
            
            if (theme.contains("Dark")) {
                UIManager.setLookAndFeel(new FlatDarkLaf());
            } else {
                UIManager.setLookAndFeel(new FlatLightLaf());
            }
            
            // تعيين الخط العربي
            setupArabicFont();
            
            // تعيين اتجاه RTL
            setupRTLOrientation();
            
            logger.info("تم تعيين مظهر التطبيق: {}", theme);
            
        } catch (Exception e) {
            logger.warn("فشل في تعيين مظهر التطبيق، سيتم استخدام المظهر الافتراضي", e);
        }
    }
    
    /**
     * تعيين الخط العربي
     */
    private static void setupArabicFont() {
        String fontName = AppConfig.getInstance().getProperty("ui.font.arabic", "Tahoma");
        int fontSize = AppConfig.getInstance().getIntProperty("ui.font.size", 14);
        
        Font arabicFont = new Font(fontName, Font.PLAIN, fontSize);
        
        // تطبيق الخط على جميع مكونات UI
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("TextArea.font", arabicFont);
        UIManager.put("ComboBox.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("TableHeader.font", arabicFont);
        UIManager.put("Menu.font", arabicFont);
        UIManager.put("MenuItem.font", arabicFont);
        UIManager.put("TabbedPane.font", arabicFont);
        UIManager.put("Tree.font", arabicFont);
        UIManager.put("List.font", arabicFont);
        UIManager.put("CheckBox.font", arabicFont);
        UIManager.put("RadioButton.font", arabicFont);
        UIManager.put("TitledBorder.font", arabicFont);
        
        logger.info("تم تعيين الخط العربي: {} بحجم {}", fontName, fontSize);
    }
    
    /**
     * تعيين اتجاه RTL
     */
    private static void setupRTLOrientation() {
        boolean rtlSupport = AppConfig.getInstance().getBooleanProperty("app.rtl", true);
        
        if (rtlSupport) {
            // تعيين اتجاه RTL للمكونات
            UIManager.put("TextComponent.orientation", ComponentOrientation.RIGHT_TO_LEFT);
            UIManager.put("Table.componentOrientation", ComponentOrientation.RIGHT_TO_LEFT);
            UIManager.put("Tree.componentOrientation", ComponentOrientation.RIGHT_TO_LEFT);
            UIManager.put("List.componentOrientation", ComponentOrientation.RIGHT_TO_LEFT);
            
            logger.info("تم تفعيل دعم RTL");
        }
    }
    
    /**
     * تهيئة قاعدة البيانات
     */
    private static boolean initializeDatabase() {
        try {
            DatabaseManager dbManager = DatabaseManager.getInstance();
            boolean connected = dbManager.testConnection();
            
            if (connected) {
                logger.info("تم الاتصال بقاعدة البيانات بنجاح");
                return true;
            } else {
                logger.error("فشل في الاتصال بقاعدة البيانات");
                return false;
            }
            
        } catch (Exception e) {
            logger.error("خطأ في تهيئة قاعدة البيانات", e);
            return false;
        }
    }
    
    /**
     * عرض شاشة تسجيل الدخول
     */
    private static void showLoginDialog() {
        LoginDialog loginDialog = new LoginDialog();
        loginDialog.setVisible(true);
        
        // إذا تم تسجيل الدخول بنجاح، عرض الشاشة الرئيسية
        if (loginDialog.isLoginSuccessful()) {
            SwingUtilities.invokeLater(() -> {
                MainFrame mainFrame = new MainFrame();
                mainFrame.setVisible(true);
            });
        } else {
            // إذا تم إلغاء تسجيل الدخول، إغلاق التطبيق
            System.exit(0);
        }
    }
    
    /**
     * عرض رسالة خطأ
     */
    private static void showErrorDialog(String message) {
        JOptionPane.showMessageDialog(
            null,
            message,
            "خطأ",
            JOptionPane.ERROR_MESSAGE
        );
    }
}
