@echo off
chcp 65001 > nul
echo ========================================
echo نظام إدارة الشحنات - إنشاء مستخدم قاعدة البيانات
echo Shipment Management System - Database User Creation
echo ========================================
echo.

echo يرجى إدخال بيانات الاتصال بقاعدة البيانات Oracle:
echo Please enter Oracle database connection details:
echo.

set /p ORACLE_HOST="عنوان الخادم (localhost): "
if "%ORACLE_HOST%"=="" set ORACLE_HOST=localhost

set /p ORACLE_PORT="رقم المنفذ (1521): "
if "%ORACLE_PORT%"=="" set ORACLE_PORT=1521

set /p ORACLE_SID="اسم قاعدة البيانات (ORCL): "
if "%ORACLE_SID%"=="" set ORACLE_SID=ORCL

set /p SYSTEM_PASSWORD="كلمة مرور مستخدم SYSTEM: "

echo.
echo جاري إنشاء مستخدم ship_erp...
echo Creating user ship_erp...
echo.

echo -- إنشاء مستخدم قاعدة البيانات > temp_create_user.sql
echo -- Create Database User >> temp_create_user.sql
echo. >> temp_create_user.sql

echo -- Drop user if exists >> temp_create_user.sql
echo BEGIN >> temp_create_user.sql
echo     EXECUTE IMMEDIATE 'DROP USER ship_erp CASCADE'; >> temp_create_user.sql
echo EXCEPTION >> temp_create_user.sql
echo     WHEN OTHERS THEN >> temp_create_user.sql
echo         IF SQLCODE != -1918 THEN >> temp_create_user.sql
echo             RAISE; >> temp_create_user.sql
echo         END IF; >> temp_create_user.sql
echo END; >> temp_create_user.sql
echo / >> temp_create_user.sql
echo. >> temp_create_user.sql

echo -- Create user >> temp_create_user.sql
echo CREATE USER ship_erp IDENTIFIED BY ys123; >> temp_create_user.sql
echo. >> temp_create_user.sql

echo -- Grant privileges >> temp_create_user.sql
echo GRANT CONNECT TO ship_erp; >> temp_create_user.sql
echo GRANT RESOURCE TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE SESSION TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE TABLE TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE VIEW TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE SEQUENCE TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE PROCEDURE TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE TRIGGER TO ship_erp; >> temp_create_user.sql
echo GRANT CREATE SYNONYM TO ship_erp; >> temp_create_user.sql
echo GRANT UNLIMITED TABLESPACE TO ship_erp; >> temp_create_user.sql
echo. >> temp_create_user.sql

echo -- Verify user creation >> temp_create_user.sql
echo SELECT username, account_status, default_tablespace FROM dba_users WHERE username = 'SHIP_ERP'; >> temp_create_user.sql
echo. >> temp_create_user.sql

echo COMMIT; >> temp_create_user.sql
echo EXIT; >> temp_create_user.sql

echo تشغيل سكريبت إنشاء المستخدم...
echo Running user creation script...

sqlplus system/%SYSTEM_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SID% @temp_create_user.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم إنشاء المستخدم بنجاح!
    echo ✓ User created successfully!
    echo.
    echo اسم المستخدم: ship_erp
    echo كلمة المرور: ys123
    echo Username: ship_erp
    echo Password: ys123
    echo.
    
    echo جاري اختبار الاتصال...
    echo Testing connection...
    
    echo SELECT 'Connection successful!' FROM dual; > test_connection.sql
    echo EXIT; >> test_connection.sql
    
    sqlplus ship_erp/ys123@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SID% @test_connection.sql
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✓ تم اختبار الاتصال بنجاح!
        echo ✓ Connection test successful!
        echo.
        echo يمكنك الآن تشغيل التطبيق
        echo You can now run the application
    ) else (
        echo.
        echo ✗ فشل في اختبار الاتصال
        echo ✗ Connection test failed
    )
    
    del test_connection.sql
) else (
    echo.
    echo ✗ فشل في إنشاء المستخدم
    echo ✗ Failed to create user
    echo يرجى التحقق من بيانات الاتصال وإعادة المحاولة
    echo Please check connection details and try again
)

del temp_create_user.sql

echo.
pause
