# نظام إدارة الشحنات المتكامل
## Comprehensive Shipment Management System

### الإصدار: 1.0.0
### Version: 1.0.0

---

## نظرة عامة | Overview

نظام إدارة الشحنات المتكامل هو تطبيق Java متقدم مصمم خصيصاً لإدارة عمليات الشحن والخدمات اللوجستية مع دعم كامل للغة العربية وترميز AR8MSWIN1256.

The Comprehensive Shipment Management System is an advanced Java application specifically designed for managing shipping operations and logistics services with full Arabic language support and AR8MSWIN1256 encoding.

---

## الميزات الرئيسية | Key Features

### ✅ تم التنفيذ | Implemented
- **دعم كامل للغة العربية** مع ترميز AR8MSWIN1256
- **واجهة مستخدم RTL** (من اليمين إلى اليسار)
- **قاعدة بيانات Oracle** مع دعم الترميز العربي
- **نظام تسجيل الدخول** الآمن
- **الواجهة الرئيسية** مع شجرة الأنظمة
- **نظام الإعدادات العامة** (الهيكل الأساسي)

### 🚧 قيد التطوير | Under Development
- نظام إدارة الأصناف
- نظام إدارة الموردين  
- نظام إدارة متابعة وتتبع الشحنات
- نظام الإدخالات الجمركية
- نظام التكاليف

---

## المتطلبات التقنية | Technical Requirements

### البرمجيات المطلوبة | Required Software
- **Java 17** أو أحدث
- **Oracle Database 11g** أو أحدث
- **Maven 3.6+** (اختياري)

### قاعدة البيانات | Database
- **المستخدم**: ship_erp
- **كلمة المرور**: ys123
- **الترميز**: AR8MSWIN1256
- **اللغة**: العربية
- **المنطقة**: السعودية

---

## التثبيت والإعداد | Installation & Setup

### 1. إعداد قاعدة البيانات | Database Setup

```bash
# تشغيل سكريبت إنشاء المستخدم
# Run user creation script
sqlplus / as sysdba
```

```sql
-- إنشاء المستخدم
CREATE USER ship_erp IDENTIFIED BY ys123;
GRANT CONNECT, RESOURCE TO ship_erp;
GRANT CREATE SESSION TO ship_erp;
GRANT CREATE TABLE TO ship_erp;
GRANT CREATE VIEW TO ship_erp;
GRANT CREATE SEQUENCE TO ship_erp;
GRANT UNLIMITED TABLESPACE TO ship_erp;
```

### 2. إنشاء الجداول | Create Tables

```bash
# الاتصال بالمستخدم الجديد
sqlplus ship_erp/ys123@localhost:1521/ORCL

# تشغيل سكريبت إنشاء الجداول
@src/main/resources/database/create_tables.sql
```

### 3. تشغيل التطبيق | Run Application

```bash
# باستخدام Maven
mvn clean compile exec:java

# أو باستخدام السكريبت
run_application.bat
```

---

## هيكل المشروع | Project Structure

```
shipment-management-system/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/shipment/erp/
│   │   │       ├── ShipmentManagementApp.java
│   │   │       ├── config/
│   │   │       │   └── AppConfig.java
│   │   │       ├── database/
│   │   │       │   └── DatabaseManager.java
│   │   │       ├── ui/
│   │   │       │   ├── MainFrame.java
│   │   │       │   └── login/
│   │   │       │       └── LoginDialog.java
│   │   │       └── util/
│   │   │           └── MessageManager.java
│   │   └── resources/
│   │       ├── application.properties
│   │       ├── messages_ar.properties
│   │       ├── logback.xml
│   │       └── database/
│   │           ├── create_user.sql
│   │           └── create_tables.sql
│   └── test/
├── lib/
├── target/
├── pom.xml
├── run_application.bat
└── README.md
```

---

## قاعدة البيانات | Database Schema

### الجداول الرئيسية | Main Tables

1. **COMPANY_DATA** - بيانات الشركة
2. **BRANCHES** - الفروع
3. **USERS** - المستخدمين
4. **ROLES** - الأدوار
5. **PERMISSIONS** - الصلاحيات
6. **ROLE_PERMISSIONS** - صلاحيات الأدوار
7. **CURRENCIES** - العملات
8. **FISCAL_YEARS** - السنوات المالية
9. **SYSTEM_VARIABLES** - المتغيرات العامة

---

## الإعدادات | Configuration

### ملف application.properties

```properties
# إعدادات قاعدة البيانات
db.url=*************************************
db.username=ship_erp
db.password=ys123
db.charset=AR8MSWIN1256

# إعدادات الواجهة
ui.font.arabic=Tahoma
ui.font.size=14
app.rtl=true
```

---

## المكتبات المستخدمة | Used Libraries

- **FlatLaf** - مظهر عصري للواجهة
- **MigLayout** - مدير التخطيط المتقدم
- **Oracle JDBC** - تعريف قاعدة البيانات
- **HikariCP** - مجموعة اتصالات قاعدة البيانات
- **Logback** - نظام السجلات
- **Jackson** - معالجة JSON

---

## بيانات تسجيل الدخول الافتراضية | Default Login

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

---

## الدعم الفني | Technical Support

### المشاكل الشائعة | Common Issues

1. **مشكلة الترميز العربي**
   - تأكد من تعيين ترميز AR8MSWIN1256
   - استخدم `ALTER SESSION SET NLS_LANGUAGE='ARABIC'`

2. **مشكلة الاتصال بقاعدة البيانات**
   - تحقق من تشغيل خدمة Oracle
   - تأكد من صحة بيانات الاتصال

3. **مشكلة عرض النصوص العربية**
   - تأكد من استخدام خط Tahoma أو خط عربي آخر
   - تحقق من إعدادات RTL

---

## خطة التطوير المستقبلية | Future Development Plan

### المرحلة الثانية | Phase 2
- تطوير نظام إدارة الأصناف
- إضافة تقارير متقدمة
- تحسين الأمان

### المرحلة الثالثة | Phase 3
- تطوير نظام إدارة الموردين
- إضافة واجهة ويب
- تكامل مع أنظمة خارجية

### المرحلة الرابعة | Phase 4
- تطوير نظام تتبع الشحنات
- إضافة تطبيق موبايل
- ذكاء اصطناعي للتنبؤات

---

## الترخيص | License

هذا المشروع مطور لأغراض تعليمية وتجارية.
This project is developed for educational and commercial purposes.

---

## معلومات الاتصال | Contact Information

للدعم الفني والاستفسارات:
For technical support and inquiries:

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-1234567

---

**© 2025 نظام إدارة الشحنات المتكامل - جميع الحقوق محفوظة**
**© 2025 Comprehensive Shipment Management System - All Rights Reserved**
