-- إنشاء جداول نظام إدارة الشحنات
-- Create Tables for Shipment Management System

-- Connect as ship_erp user
-- sqlplus ship_erp/ys123@localhost:1521/XE

-- ===== جدول بيانات الشركة - Company Data =====
CREATE TABLE COMPANY_DATA (
    COMPANY_ID NUMBER(10) PRIMARY KEY,
    COMPANY_NAME NVARCHAR2(200) NOT NULL,
    COMPANY_NAME_EN VARCHAR2(200),
    ADDRESS NVARCHAR2(500),
    PHONE VARCHAR2(50),
    FAX VARCHAR2(50),
    EMAIL VARCHAR2(100),
    WEBSITE VARCHAR2(100),
    TAX_NUMBER VARCHAR2(50),
    COMMERCIAL_REGISTER VARCHAR2(50),
    LOGO BLOB,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    MODIFIED_BY NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1
);

-- ===== جدول الفروع - Branches =====
CREATE TABLE BRANCHES (
    BRANCH_ID NUMBER(10) PRIMARY KEY,
    BRANCH_CODE VARCHAR2(20) UNIQUE NOT NULL,
    BRANCH_NAME NVARCHAR2(200) NOT NULL,
    BRANCH_NAME_EN VARCHAR2(200),
    ADDRESS NVARCHAR2(500),
    PHONE VARCHAR2(50),
    FAX VARCHAR2(50),
    EMAIL VARCHAR2(100),
    MANAGER_NAME NVARCHAR2(100),
    COMPANY_ID NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    MODIFIED_BY NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CONSTRAINT FK_BRANCHES_COMPANY FOREIGN KEY (COMPANY_ID) REFERENCES COMPANY_DATA(COMPANY_ID)
);

-- ===== جدول المستخدمين - Users =====
CREATE TABLE USERS (
    USER_ID NUMBER(10) PRIMARY KEY,
    USERNAME VARCHAR2(50) UNIQUE NOT NULL,
    PASSWORD VARCHAR2(255) NOT NULL,
    FULL_NAME NVARCHAR2(200) NOT NULL,
    EMAIL VARCHAR2(100),
    PHONE VARCHAR2(50),
    ROLE_ID NUMBER(10),
    BRANCH_ID NUMBER(10),
    LAST_LOGIN DATE,
    LOGIN_ATTEMPTS NUMBER(2) DEFAULT 0,
    IS_LOCKED NUMBER(1) DEFAULT 0,
    PASSWORD_EXPIRES DATE,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    MODIFIED_BY NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CONSTRAINT FK_USERS_BRANCH FOREIGN KEY (BRANCH_ID) REFERENCES BRANCHES(BRANCH_ID)
);

-- ===== جدول الأدوار - Roles =====
CREATE TABLE ROLES (
    ROLE_ID NUMBER(10) PRIMARY KEY,
    ROLE_NAME NVARCHAR2(100) NOT NULL,
    ROLE_NAME_EN VARCHAR2(100),
    DESCRIPTION NVARCHAR2(500),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    MODIFIED_BY NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1
);

-- ===== جدول الصلاحيات - Permissions =====
CREATE TABLE PERMISSIONS (
    PERMISSION_ID NUMBER(10) PRIMARY KEY,
    PERMISSION_NAME VARCHAR2(100) NOT NULL,
    PERMISSION_NAME_AR NVARCHAR2(100),
    MODULE_NAME VARCHAR2(50),
    DESCRIPTION NVARCHAR2(500),
    CREATED_DATE DATE DEFAULT SYSDATE,
    IS_ACTIVE NUMBER(1) DEFAULT 1
);

-- ===== جدول صلاحيات الأدوار - Role Permissions =====
CREATE TABLE ROLE_PERMISSIONS (
    ROLE_ID NUMBER(10),
    PERMISSION_ID NUMBER(10),
    CAN_VIEW NUMBER(1) DEFAULT 0,
    CAN_ADD NUMBER(1) DEFAULT 0,
    CAN_EDIT NUMBER(1) DEFAULT 0,
    CAN_DELETE NUMBER(1) DEFAULT 0,
    CAN_PRINT NUMBER(1) DEFAULT 0,
    GRANTED_DATE DATE DEFAULT SYSDATE,
    GRANTED_BY NUMBER(10),
    PRIMARY KEY (ROLE_ID, PERMISSION_ID),
    CONSTRAINT FK_ROLE_PERM_ROLE FOREIGN KEY (ROLE_ID) REFERENCES ROLES(ROLE_ID),
    CONSTRAINT FK_ROLE_PERM_PERMISSION FOREIGN KEY (PERMISSION_ID) REFERENCES PERMISSIONS(PERMISSION_ID)
);

-- ===== جدول العملات - Currencies =====
CREATE TABLE CURRENCIES (
    CURRENCY_ID NUMBER(10) PRIMARY KEY,
    CURRENCY_CODE VARCHAR2(3) UNIQUE NOT NULL,
    CURRENCY_NAME NVARCHAR2(100) NOT NULL,
    CURRENCY_NAME_EN VARCHAR2(100),
    CURRENCY_SYMBOL VARCHAR2(10),
    EXCHANGE_RATE NUMBER(15,6) DEFAULT 1,
    IS_DEFAULT NUMBER(1) DEFAULT 0,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    MODIFIED_BY NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1
);

-- ===== جدول السنوات المالية - Fiscal Years =====
CREATE TABLE FISCAL_YEARS (
    FISCAL_YEAR_ID NUMBER(10) PRIMARY KEY,
    YEAR_CODE VARCHAR2(10) UNIQUE NOT NULL,
    YEAR_NAME NVARCHAR2(100) NOT NULL,
    START_DATE DATE NOT NULL,
    END_DATE DATE NOT NULL,
    IS_CURRENT NUMBER(1) DEFAULT 0,
    IS_CLOSED NUMBER(1) DEFAULT 0,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    CLOSED_DATE DATE,
    CLOSED_BY NUMBER(10)
);

-- ===== جدول المتغيرات العامة - System Variables =====
CREATE TABLE SYSTEM_VARIABLES (
    VARIABLE_ID NUMBER(10) PRIMARY KEY,
    VARIABLE_NAME VARCHAR2(100) UNIQUE NOT NULL,
    VARIABLE_NAME_AR NVARCHAR2(100),
    VARIABLE_VALUE NVARCHAR2(1000),
    VARIABLE_TYPE VARCHAR2(20) DEFAULT 'STRING',
    DESCRIPTION NVARCHAR2(500),
    MODULE_NAME VARCHAR2(50),
    IS_EDITABLE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    MODIFIED_BY NUMBER(10)
);

-- Create Sequences
CREATE SEQUENCE SEQ_COMPANY_DATA START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_BRANCHES START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_USERS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_ROLES START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_PERMISSIONS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_CURRENCIES START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_FISCAL_YEARS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_VARIABLES START WITH 1 INCREMENT BY 1;

-- Add Foreign Key Constraints
ALTER TABLE USERS ADD CONSTRAINT FK_USERS_ROLE FOREIGN KEY (ROLE_ID) REFERENCES ROLES(ROLE_ID);

-- Create Indexes for Performance
CREATE INDEX IDX_USERS_USERNAME ON USERS(USERNAME);
CREATE INDEX IDX_USERS_BRANCH ON USERS(BRANCH_ID);
CREATE INDEX IDX_BRANCHES_CODE ON BRANCHES(BRANCH_CODE);
CREATE INDEX IDX_CURRENCIES_CODE ON CURRENCIES(CURRENCY_CODE);
CREATE INDEX IDX_FISCAL_YEARS_CURRENT ON FISCAL_YEARS(IS_CURRENT);

-- Insert Initial Data
-- ===== بيانات الشركة الافتراضية =====
INSERT INTO COMPANY_DATA (COMPANY_ID, COMPANY_NAME, COMPANY_NAME_EN, ADDRESS, PHONE, EMAIL, CREATED_BY)
VALUES (SEQ_COMPANY_DATA.NEXTVAL, 'شركة إدارة الشحنات', 'Shipment Management Company',
        'الرياض، المملكة العربية السعودية', '+966-11-1234567', '<EMAIL>', 1);

-- ===== الفرع الرئيسي =====
INSERT INTO BRANCHES (BRANCH_ID, BRANCH_CODE, BRANCH_NAME, BRANCH_NAME_EN, ADDRESS, PHONE, COMPANY_ID, CREATED_BY)
VALUES (SEQ_BRANCHES.NEXTVAL, 'MAIN', 'الفرع الرئيسي', 'Main Branch',
        'الرياض، المملكة العربية السعودية', '+966-11-1234567', 1, 1);

-- ===== الأدوار الأساسية =====
INSERT INTO ROLES (ROLE_ID, ROLE_NAME, ROLE_NAME_EN, DESCRIPTION, CREATED_BY)
VALUES (SEQ_ROLES.NEXTVAL, 'مدير النظام', 'System Administrator', 'مدير النظام الرئيسي', 1);

INSERT INTO ROLES (ROLE_ID, ROLE_NAME, ROLE_NAME_EN, DESCRIPTION, CREATED_BY)
VALUES (SEQ_ROLES.NEXTVAL, 'مدير', 'Manager', 'مدير الفرع', 1);

INSERT INTO ROLES (ROLE_ID, ROLE_NAME, ROLE_NAME_EN, DESCRIPTION, CREATED_BY)
VALUES (SEQ_ROLES.NEXTVAL, 'موظف', 'Employee', 'موظف عادي', 1);

-- ===== المستخدم الافتراضي =====
INSERT INTO USERS (USER_ID, USERNAME, PASSWORD, FULL_NAME, EMAIL, ROLE_ID, BRANCH_ID, CREATED_BY)
VALUES (SEQ_USERS.NEXTVAL, 'admin', 'admin123', 'مدير النظام', '<EMAIL>', 1, 1, 1);

-- ===== العملات الأساسية =====
INSERT INTO CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME, CURRENCY_NAME_EN, CURRENCY_SYMBOL, IS_DEFAULT, CREATED_BY)
VALUES (SEQ_CURRENCIES.NEXTVAL, 'SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1, 1);

INSERT INTO CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME, CURRENCY_NAME_EN, CURRENCY_SYMBOL, EXCHANGE_RATE, CREATED_BY)
VALUES (SEQ_CURRENCIES.NEXTVAL, 'USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 1);

INSERT INTO CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME, CURRENCY_NAME_EN, CURRENCY_SYMBOL, EXCHANGE_RATE, CREATED_BY)
VALUES (SEQ_CURRENCIES.NEXTVAL, 'EUR', 'يورو', 'Euro', '€', 4.10, 1);

-- ===== السنة المالية الحالية =====
INSERT INTO FISCAL_YEARS (FISCAL_YEAR_ID, YEAR_CODE, YEAR_NAME, START_DATE, END_DATE, IS_CURRENT, CREATED_BY)
VALUES (SEQ_FISCAL_YEARS.NEXTVAL, '2025', 'السنة المالية 2025',
        TO_DATE('01/01/2025', 'DD/MM/YYYY'), TO_DATE('31/12/2025', 'DD/MM/YYYY'), 1, 1);

-- ===== المتغيرات العامة =====
INSERT INTO SYSTEM_VARIABLES (VARIABLE_ID, VARIABLE_NAME, VARIABLE_NAME_AR, VARIABLE_VALUE, DESCRIPTION, CREATED_BY)
VALUES (SEQ_SYSTEM_VARIABLES.NEXTVAL, 'APP_TITLE', 'عنوان التطبيق', 'نظام إدارة الشحنات', 'عنوان التطبيق الرئيسي', 1);

INSERT INTO SYSTEM_VARIABLES (VARIABLE_ID, VARIABLE_NAME, VARIABLE_NAME_AR, VARIABLE_VALUE, DESCRIPTION, CREATED_BY)
VALUES (SEQ_SYSTEM_VARIABLES.NEXTVAL, 'DEFAULT_LANGUAGE', 'اللغة الافتراضية', 'ar', 'اللغة الافتراضية للنظام', 1);

INSERT INTO SYSTEM_VARIABLES (VARIABLE_ID, VARIABLE_NAME, VARIABLE_NAME_AR, VARIABLE_VALUE, DESCRIPTION, CREATED_BY)
VALUES (SEQ_SYSTEM_VARIABLES.NEXTVAL, 'RTL_SUPPORT', 'دعم الكتابة من اليمين لليسار', '1', 'تفعيل دعم RTL', 1);

COMMIT;
