@echo off
chcp 65001 > nul
title نظام إدارة الشحنات - تثبيت النظام
color 0A

echo ========================================
echo نظام إدارة الشحنات المتكامل
echo Comprehensive Shipment Management System
echo ========================================
echo الإصدار: 1.0.0
echo Version: 1.0.0
echo ========================================
echo.

echo مرحباً بك في معالج تثبيت نظام إدارة الشحنات
echo Welcome to Shipment Management System Installation Wizard
echo.

echo سيقوم هذا المعالج بـ:
echo This wizard will:
echo 1. إنشاء مستخدم قاعدة البيانات ship_erp
echo    Create database user ship_erp
echo 2. إنشاء الجداول والبيانات الأساسية
echo    Create tables and initial data
echo 3. اختبار الاتصال والتأكد من التثبيت
echo    Test connection and verify installation
echo.

set /p CONTINUE="هل تريد المتابعة؟ (Y/N): "
if /i not "%CONTINUE%"=="Y" goto :END

echo.
echo ========================================
echo الخطوة 1: إنشاء مستخدم قاعدة البيانات
echo Step 1: Creating Database User
echo ========================================
echo.

call create_database_user.bat

echo.
echo اضغط أي مفتاح للمتابعة للخطوة التالية...
echo Press any key to continue to next step...
pause > nul

echo.
echo ========================================
echo الخطوة 2: إنشاء الجداول والبيانات
echo Step 2: Creating Tables and Data
echo ========================================
echo.

call create_tables.bat

echo.
echo اضغط أي مفتاح للمتابعة للخطوة التالية...
echo Press any key to continue to next step...
pause > nul

echo.
echo ========================================
echo الخطوة 3: اختبار النظام
echo Step 3: System Testing
echo ========================================
echo.

call test_database_connection.bat

echo.
echo ========================================
echo تم الانتهاء من التثبيت!
echo Installation Completed!
echo ========================================
echo.

echo معلومات الاتصال:
echo Connection Information:
echo - اسم المستخدم / Username: ship_erp
echo - كلمة المرور / Password: ys123
echo - قاعدة البيانات / Database: ORCL (أو حسب إعدادك)
echo.

echo لتشغيل التطبيق:
echo To run the application:
echo 1. تأكد من تشغيل خدمة Oracle
echo    Make sure Oracle service is running
echo 2. شغل الأمر: java -jar target/shipment-management-system-1.0.0.jar
echo    Run: java -jar target/shipment-management-system-1.0.0.jar
echo.

echo أو استخدم:
echo Or use:
echo mvn clean compile exec:java
echo.

:END
echo.
echo شكراً لاستخدام نظام إدارة الشحنات!
echo Thank you for using Shipment Management System!
echo.
pause
